/**
 * Browser and environment detection utilities
 * @module browser/detection
 */

/**
 * Check if code is running in a browser environment
 * @returns True if running in browser
 * @example
 * ```typescript
 * if (isBrowser()) {
 *   // Browser-specific code
 *   document.getElementById('app');
 * }
 * ```
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
}

/**
 * Check if code is running in a Node.js environment
 * @returns True if running in Node.js
 */
export function isNode(): boolean {
  return process?.versions?.node != null;
}

/**
 * Check if code is running in a Web Worker
 * @returns True if running in Web Worker
 */
export function isWebWorker(): boolean {
  return typeof importScripts === 'function' && 
         typeof navigator !== 'undefined';
}

/**
 * Check if code is running in a Service Worker
 * @returns True if running in Service Worker
 */
export function isServiceWorker(): boolean {
  return typeof importScripts === 'function' && 
         typeof navigator !== 'undefined' &&
         'serviceWorker' in navigator;
}

/**
 * Check if the browser is mobile
 * @returns True if mobile browser
 */
export function isMobile(): boolean {
  if (!isBrowser()) return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent,
  );
}

/**
 * Check if the browser is on iOS
 * @returns True if iOS browser
 */
export function isIOS(): boolean {
  if (!isBrowser()) return false;
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * Check if the browser is on Android
 * @returns True if Android browser
 */
export function isAndroid(): boolean {
  if (!isBrowser()) return false;
  
  return /Android/.test(navigator.userAgent);
}

/**
 * Check if the browser is Safari
 * @returns True if Safari browser
 */
export function isSafari(): boolean {
  if (!isBrowser()) return false;
  
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

/**
 * Check if the browser is Chrome
 * @returns True if Chrome browser
 */
export function isChrome(): boolean {
  if (!isBrowser()) return false;
  
  return /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
}

/**
 * Check if the browser is Firefox
 * @returns True if Firefox browser
 */
export function isFirefox(): boolean {
  if (!isBrowser()) return false;
  
  return /Firefox/.test(navigator.userAgent);
}

/**
 * Check if the browser is Edge
 * @returns True if Edge browser
 */
export function isEdge(): boolean {
  if (!isBrowser()) return false;
  
  return /Edg/.test(navigator.userAgent);
}

/**
 * Check if the browser is Internet Explorer
 * @returns True if IE browser
 */
export function isIE(): boolean {
  if (!isBrowser()) return false;
  
  return /MSIE|Trident/.test(navigator.userAgent);
}

/**
 * Check if the browser is QQ Browser
 * @returns True if QQ browser
 */
export function isQQ(): boolean {
  if (!isBrowser()) return false;
  
  return /QQ/i.test(navigator.userAgent);
}

/**
 * Check if the browser is UC Browser
 * @returns True if UC browser
 */
export function isUC(): boolean {
  if (!isBrowser()) return false;
  
  return /UC/i.test(navigator.userAgent);
}

/**
 * Check if the browser is DingTalk
 * @returns True if DingTalk browser
 */
export function isDingTalk(): boolean {
  if (!isBrowser()) return false;
  
  return /dingtalk/i.test(navigator.userAgent);
}

/**
 * Check if the browser is Alipay
 * @returns True if Alipay browser
 */
export function isAlipay(): boolean {
  if (!isBrowser()) return false;
  
  return /Alipay/i.test(navigator.userAgent);
}

/**
 * Get Chrome/Chromium version
 * @returns Chrome version number or null if not Chrome-based
 */
export function getChromiumVersion(): number | null {
  if (!isBrowser()) return null;
  
  const userAgent = navigator.userAgent;
  const match = userAgent.match(/(?:Chromium|Chrome)\/(\d+)/);
  return match ? parseInt(match[1], 10) : null;
}

/**
 * Get browser information
 * @returns Object containing browser details
 */
export interface BrowserInfo {
  name: string;
  version: string;
  engine: string;
  platform: string;
  mobile: boolean;
}

export function getBrowserInfo(): BrowserInfo | null {
  if (!isBrowser()) return null;
  
  const ua = navigator.userAgent;
  const platform = navigator.platform;
  
  let name = 'Unknown';
  let version = 'Unknown';
  let engine = 'Unknown';
  
  // Detect browser name and version
  if (isChrome()) {
    name = 'Chrome';
    const match = ua.match(/Chrome\/(\d+\.\d+)/);
    version = match ? match[1] : 'Unknown';
    engine = 'Blink';
  } else if (isFirefox()) {
    name = 'Firefox';
    const match = ua.match(/Firefox\/(\d+\.\d+)/);
    version = match ? match[1] : 'Unknown';
    engine = 'Gecko';
  } else if (isSafari()) {
    name = 'Safari';
    const match = ua.match(/Version\/(\d+\.\d+)/);
    version = match ? match[1] : 'Unknown';
    engine = 'WebKit';
  } else if (isEdge()) {
    name = 'Edge';
    const match = ua.match(/Edg\/(\d+\.\d+)/);
    version = match ? match[1] : 'Unknown';
    engine = 'Blink';
  } else if (isIE()) {
    name = 'Internet Explorer';
    const match = ua.match(/(?:MSIE |rv:)(\d+\.\d+)/);
    version = match ? match[1] : 'Unknown';
    engine = 'Trident';
  }
  
  return {
    name,
    version,
    engine,
    platform,
    mobile: isMobile(),
  };
}

/**
 * Check if a specific feature is supported
 * @param feature - The feature to check
 * @returns True if the feature is supported
 */
export function isFeatureSupported(feature: string): boolean {
  if (!isBrowser()) return false;
  
  const features: Record<string, () => boolean> = {
    localStorage: () => {
      try {
        const test = '__test__';
        localStorage.setItem(test, test);
        localStorage.removeItem(test);
        return true;
      } catch {
        return false;
      }
    },
    sessionStorage: () => {
      try {
        const test = '__test__';
        sessionStorage.setItem(test, test);
        sessionStorage.removeItem(test);
        return true;
      } catch {
        return false;
      }
    },
    webGL: () => {
      try {
        const canvas = document.createElement('canvas');
        return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
      } catch {
        return false;
      }
    },
    webGL2: () => {
      try {
        const canvas = document.createElement('canvas');
        return !!canvas.getContext('webgl2');
      } catch {
        return false;
      }
    },
    webWorkers: () => typeof Worker !== 'undefined',
    serviceWorkers: () => 'serviceWorker' in navigator,
    pushNotifications: () => 'PushManager' in window,
    geolocation: () => 'geolocation' in navigator,
    camera: () => 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
    fullscreen: () => 'requestFullscreen' in document.documentElement,
    clipboard: () => 'clipboard' in navigator,
    share: () => 'share' in navigator,
    bluetooth: () => 'bluetooth' in navigator,
    usb: () => 'usb' in navigator,
  };
  
  const checker = features[feature];
  return checker ? checker() : false;
}
