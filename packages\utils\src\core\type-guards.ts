/**
 * Type guard utility functions
 * @module core/type-guards
 */

/**
 * Check if a value is null
 * @param value - The value to check
 * @returns True if the value is null
 */
export function isNull(value: unknown): value is null {
  return value === null;
}

/**
 * Check if a value is undefined
 * @param value - The value to check
 * @returns True if the value is undefined
 */
export function isUndefined(value: unknown): value is undefined {
  return value === undefined;
}

/**
 * Check if a value is null or undefined
 * @param value - The value to check
 * @returns True if the value is null or undefined
 */
export function isNullish(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * Check if a value is not null or undefined
 * @param value - The value to check
 * @returns True if the value is not null or undefined
 */
export function isNotNullish<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Check if a value is a boolean
 * @param value - The value to check
 * @returns True if the value is a boolean
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * Check if a value is a symbol
 * @param value - The value to check
 * @returns True if the value is a symbol
 */
export function isSymbol(value: unknown): value is symbol {
  return typeof value === 'symbol';
}

/**
 * Check if a value is a bigint
 * @param value - The value to check
 * @returns True if the value is a bigint
 */
export function isBigInt(value: unknown): value is bigint {
  return typeof value === 'bigint';
}

/**
 * Check if a value is a Date object
 * @param value - The value to check
 * @returns True if the value is a Date
 */
export function isDate(value: unknown): value is Date {
  return value instanceof Date;
}

/**
 * Check if a value is a valid Date object (not Invalid Date)
 * @param value - The value to check
 * @returns True if the value is a valid Date
 */
export function isValidDate(value: unknown): value is Date {
  return isDate(value) && !isNaN(value.getTime());
}

/**
 * Check if a value is a RegExp object
 * @param value - The value to check
 * @returns True if the value is a RegExp
 */
export function isRegExp(value: unknown): value is RegExp {
  return value instanceof RegExp;
}

/**
 * Check if a value is an Error object
 * @param value - The value to check
 * @returns True if the value is an Error
 */
export function isError(value: unknown): value is Error {
  return value instanceof Error;
}

/**
 * Check if a value is a Promise
 * @param value - The value to check
 * @returns True if the value is a Promise
 */
export function isPromise<T = unknown>(value: unknown): value is Promise<T> {
  return value instanceof Promise || (
    typeof value === 'object' &&
    value !== null &&
    typeof (value as any).then === 'function'
  );
}

/**
 * Check if a value is a Map
 * @param value - The value to check
 * @returns True if the value is a Map
 */
export function isMap<K = unknown, V = unknown>(value: unknown): value is Map<K, V> {
  return value instanceof Map;
}

/**
 * Check if a value is a Set
 * @param value - The value to check
 * @returns True if the value is a Set
 */
export function isSet<T = unknown>(value: unknown): value is Set<T> {
  return value instanceof Set;
}

/**
 * Check if a value is a WeakMap
 * @param value - The value to check
 * @returns True if the value is a WeakMap
 */
export function isWeakMap(value: unknown): value is WeakMap<object, unknown> {
  return value instanceof WeakMap;
}

/**
 * Check if a value is a WeakSet
 * @param value - The value to check
 * @returns True if the value is a WeakSet
 */
export function isWeakSet(value: unknown): value is WeakSet<object> {
  return value instanceof WeakSet;
}

/**
 * Check if a value is an ArrayBuffer
 * @param value - The value to check
 * @returns True if the value is an ArrayBuffer
 */
export function isArrayBuffer(value: unknown): value is ArrayBuffer {
  return value instanceof ArrayBuffer;
}

/**
 * Check if a value is a typed array
 * @param value - The value to check
 * @returns True if the value is a typed array
 */
export function isTypedArray(value: unknown): value is 
  | Int8Array
  | Uint8Array
  | Uint8ClampedArray
  | Int16Array
  | Uint16Array
  | Int32Array
  | Uint32Array
  | Float32Array
  | Float64Array
  | BigInt64Array
  | BigUint64Array {
  return ArrayBuffer.isView(value) && !(value instanceof DataView);
}

/**
 * Check if a value is a DataView
 * @param value - The value to check
 * @returns True if the value is a DataView
 */
export function isDataView(value: unknown): value is DataView {
  return value instanceof DataView;
}

/**
 * Check if a value is a primitive type
 * @param value - The value to check
 * @returns True if the value is a primitive
 */
export function isPrimitive(value: unknown): value is string | number | boolean | symbol | bigint | null | undefined {
  return value === null || 
         value === undefined || 
         typeof value === 'string' || 
         typeof value === 'number' || 
         typeof value === 'boolean' || 
         typeof value === 'symbol' || 
         typeof value === 'bigint';
}

/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 * @param value - The value to check
 * @returns True if the value is considered empty
 */
export function isEmpty(value: unknown): boolean {
  if (isNullish(value)) return true;
  if (typeof value === 'string') return value.length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (isMap(value) || isSet(value)) return value.size === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}

/**
 * Check if a value is a valid URL string
 * @param value - The value to check
 * @returns True if the value is a valid URL
 */
export function isUrl(value: unknown): value is string {
  if (typeof value !== 'string') return false;
  
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if a value is a valid email string
 * @param value - The value to check
 * @returns True if the value is a valid email
 */
export function isEmail(value: unknown): value is string {
  if (typeof value !== 'string') return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * Check if a value is a valid UUID string
 * @param value - The value to check
 * @returns True if the value is a valid UUID
 */
export function isUuid(value: unknown): value is string {
  if (typeof value !== 'string') return false;
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
}

/**
 * Check if a value is a valid JSON string
 * @param value - The value to check
 * @returns True if the value is valid JSON
 */
export function isJsonString(value: unknown): value is string {
  if (typeof value !== 'string') return false;
  
  try {
    JSON.parse(value);
    return true;
  } catch {
    return false;
  }
}
