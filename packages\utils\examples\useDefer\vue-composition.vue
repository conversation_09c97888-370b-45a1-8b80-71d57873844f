<template>
  <div class="use-defer-demo">
    <div class="header">
      <h1>🚀 Vue 3 + useDefer 组合式 API 示例</h1>
      <p>演示在 Vue 3 组合式 API 中使用 @nuas/utils 的 useDefer 函数</p>
    </div>

    <div class="demo-container">
      <div class="controls">
        <button
          :disabled="isRendering"
          @click="startRender(100)"
        >
          渲染 100 个元素
        </button>
        <button
          :disabled="isRendering"
          @click="startRender(500)"
        >
          渲染 500 个元素
        </button>
        <button
          :disabled="isRendering"
          @click="startRender(1000)"
        >
          渲染 1000 个元素
        </button>
        <button
          :disabled="!isRendering"
          @click="pauseRender"
        >
          暂停
        </button>
        <button
          :disabled="!isPaused"
          @click="resumeRender"
        >
          恢复
        </button>
        <button @click="resetRender">
          重置
        </button>
      </div>

      <div class="stats">
        <div class="stat-item">
          <span class="stat-value">{{ currentFrame }}</span>
          <span class="stat-label">当前帧数</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ visibleCount }}</span>
          <span class="stat-label">已渲染元素</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ items.length }}</span>
          <span class="stat-label">总元素数</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ progress }}%</span>
          <span class="stat-label">渲染进度</span>
        </div>
      </div>

      <div class="list-container">
        <transition-group
          name="fade"
          tag="div"
        >
          <div
            v-for="(item, index) in items"
            v-show="shouldShow(index)"
            :key="item.id"
            class="list-item"
          >
            <h4>{{ item.title }}</h4>
            <p>{{ item.description }}</p>
            <div class="item-meta">
              <span class="item-index">序号: {{ index + 1 }}</span>
              <span class="item-frame">渲染帧: {{ getItemFrame(index) }}</span>
            </div>
          </div>
        </transition-group>
      </div>

      <div
        v-if="renderTime > 0"
        class="performance-info"
      >
        <h4>📊 性能统计</h4>
        <p><strong>总渲染时间:</strong> {{ renderTime.toFixed(2) }}ms</p>
        <p><strong>平均每帧渲染:</strong> {{ averagePerFrame.toFixed(2) }} 个元素</p>
        <p><strong>渲染状态:</strong> {{ renderStatus }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

// 在实际项目中使用：
// import { useDefer, type UseDeferReturn } from '@nuas/utils';

// 为了演示，这里内联类型定义和实现
interface UseDeferReturn {
  defer: (n: number) => boolean;
  getCurrentFrame: () => number;
  resetFrameCount: (value?: number) => void;
  pause: () => void;
  resume: () => void;
  cleanup: () => void;
}

function useDefer(): UseDeferReturn {
  let count = 1;
  let isRunning = true;
  let rafId: number | null = null;

  function update(): void {
    count++;
    if (isRunning) {
      rafId = requestAnimationFrame(update);
    }
  }

  rafId = requestAnimationFrame(update);

  function defer(n: number): boolean {
    if (typeof n !== 'number' || n < 0) {
      console.warn('useDefer: 参数 n 必须是非负数');
      return false;
    }
    return count >= n;
  }

  function getCurrentFrame(): number {
    return count;
  }

  function resetFrameCount(value = 1): void {
    count = value;
  }

  function pause(): void {
    isRunning = false;
  }

  function resume(): void {
    if (!isRunning) {
      isRunning = true;
      rafId = requestAnimationFrame(update);
    }
  }

  function cleanup(): void {
    isRunning = false;
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  }

  return {
    defer,
    getCurrentFrame,
    resetFrameCount,
    pause,
    resume,
    cleanup,
  };
}

// 响应式数据
const items = ref<Array<{ id: number; title: string; description: string }>>([]);
const currentFrame = ref(0);
const deferInstance = ref<UseDeferReturn | null>(null);
const updateTimer = ref<number | null>(null);
const isRendering = ref(false);
const isPaused = ref(false);
const startTime = ref(0);
const renderTime = ref(0);

// 计算属性
const visibleCount = computed(() => {
  if (!deferInstance.value) return 0;
  return items.value.filter((_, index) => shouldShow(index)).length;
});

const progress = computed(() => {
  if (items.value.length === 0) return 0;
  return Math.round((visibleCount.value / items.value.length) * 100);
});

const averagePerFrame = computed(() => {
  if (currentFrame.value === 0) return 0;
  return visibleCount.value / currentFrame.value;
});

const renderStatus = computed(() => {
  if (!isRendering.value) return '未开始';
  if (isPaused.value) return '已暂停';
  if (visibleCount.value === items.value.length) return '已完成';
  return '渲染中...';
});

// 方法
function shouldShow(index: number): boolean {
  return deferInstance.value ? deferInstance.value.defer(index) : false;
}

function getItemFrame(index: number): number {
  return index + 1;
}

function generateItems(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    title: `Vue 列表项 ${i + 1}`,
    description: `这是第 ${i + 1} 个列表项，使用 Vue 3 组合式 API + useDefer 实现分帧渲染。创建时间：${new Date().toLocaleTimeString()}`,
  }));
}

function updateFrameDisplay() {
  if (deferInstance.value) {
    currentFrame.value = deferInstance.value.getCurrentFrame();
  }

  if (isRendering.value && !isPaused.value) {
    updateTimer.value = requestAnimationFrame(updateFrameDisplay);
  }
}

function startRender(count: number) {
  // 清理之前的实例
  cleanup();

  // 生成数据
  items.value = generateItems(count);
  startTime.value = performance.now();
  isRendering.value = true;
  isPaused.value = false;
  renderTime.value = 0;

  // 创建新实例
  deferInstance.value = useDefer();

  // 开始更新显示
  updateFrameDisplay();
}

function pauseRender() {
  if (deferInstance.value) {
    deferInstance.value.pause();
    isPaused.value = true;
  }
  if (updateTimer.value) {
    cancelAnimationFrame(updateTimer.value);
    updateTimer.value = null;
  }
}

function resumeRender() {
  if (deferInstance.value) {
    deferInstance.value.resume();
    isPaused.value = false;
    updateFrameDisplay();
  }
}

function resetRender() {
  cleanup();
  items.value = [];
  currentFrame.value = 0;
  isRendering.value = false;
  isPaused.value = false;
  renderTime.value = 0;
}

function cleanup() {
  if (deferInstance.value) {
    deferInstance.value.cleanup();
    deferInstance.value = null;
  }
  if (updateTimer.value) {
    cancelAnimationFrame(updateTimer.value);
    updateTimer.value = null;
  }
}

// 监听渲染完成
watch(
  () => visibleCount.value,
  (newCount) => {
    if (newCount > 0 && newCount === items.value.length && isRendering.value) {
      renderTime.value = performance.now() - startTime.value;
      isRendering.value = false;
      isPaused.value = false;
      console.log(`Vue 渲染完成！总耗时：${renderTime.value.toFixed(2)}ms`);
    }
  },
);

// 生命周期
onMounted(() => {
  console.log('Vue useDefer 示例组件已挂载');
});

onUnmounted(() => {
  cleanup();
  console.log('Vue useDefer 示例组件已卸载');
});
</script>

<style scoped>
.use-defer-demo {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  border-radius: 12px;
}

.demo-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.controls button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background: #42b883;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.controls button:hover:not(:disabled) {
  background: #369870;
  transform: translateY(-1px);
}

.controls button:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
}

.stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  display: block;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

.list-container {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
}

.list-item {
  padding: 20px;
  margin: 10px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #42b883;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.list-item h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
}

.list-item p {
  margin: 0 0 10px 0;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
}

.item-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #95a5a6;
}

.performance-info {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.performance-info h4 {
  color: #155724;
  margin: 0 0 15px 0;
}

.performance-info p {
  color: #155724;
  margin: 8px 0;
  font-size: 14px;
}

/* 过渡动画 */
.fade-enter-active {
  transition: all 0.5s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}
</style>
