# @nuas/utils 使用示例

这个目录包含了 `@nuas/utils` 包中各种工具函数的使用示例。

## 目录结构

- `useDefer/` - useDefer 函数的使用示例
  - `basic-usage.html` - 基础 HTML 使用示例
  - `vue-composition.vue` - Vue 3 组合式 API 示例
  - `vue-options.vue` - Vue 3 选项式 API 示例
  - `react-example.tsx` - React 使用示例
  - `performance-demo.html` - 性能对比演示

## 如何运行示例

### HTML 示例
直接在浏览器中打开 `.html` 文件即可运行。

### Vue 示例
将 `.vue` 文件复制到你的 Vue 项目中使用。

### React 示例
将 `.tsx` 文件复制到你的 React 项目中使用。

## 安装依赖

确保你已经安装了 `@nuas/utils` 包：

```bash
# 如果在 workspace 内
pnpm add @nuas/utils

# 如果是外部项目
npm install @nuas/utils
# 或
yarn add @nuas/utils
# 或
pnpm add @nuas/utils
```

## 导入方式

```typescript
// 导入 useDefer 函数和类型
import { useDefer, type UseDeferReturn } from '@nuas/utils';

// 或者导入所有工具函数
import * as utils from '@nuas/utils';
const { useDefer } = utils;
```

## 主要功能

### useDefer
用于优化大量数据的渲染性能，通过分帧渲染的方式避免一次性渲染大量元素导致的页面卡顿。

**适用场景：**
- 长列表渲染优化
- 复杂DOM的分步加载
- 图片懒加载配合
- 表格数据的分批渲染
- 动画序列的分帧执行

**基本用法：**
```typescript
const { defer, cleanup } = useDefer();

// 在渲染循环中使用
elements.forEach((element, index) => {
  if (defer(index)) {
    element.style.display = 'block';
  }
});

// 组件卸载时清理
cleanup();
```
