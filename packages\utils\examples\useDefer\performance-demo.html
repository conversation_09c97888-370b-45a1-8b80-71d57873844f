<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>useDefer 性能对比演示</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: #f8f9fa;
    }
    
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
      color: white;
      border-radius: 12px;
    }
    
    .comparison-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .demo-section {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .demo-section h3 {
      margin: 0 0 15px 0;
      padding: 10px;
      border-radius: 6px;
      text-align: center;
    }
    
    .without-defer h3 {
      background: #ffebee;
      color: #c62828;
    }
    
    .with-defer h3 {
      background: #e8f5e8;
      color: #2e7d32;
    }
    
    .controls {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
      flex-wrap: wrap;
    }
    
    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }
    
    .without-defer button {
      background: #f44336;
      color: white;
    }
    
    .without-defer button:hover {
      background: #d32f2f;
    }
    
    .with-defer button {
      background: #4caf50;
      color: white;
    }
    
    .with-defer button:hover {
      background: #388e3c;
    }
    
    .stats {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 6px;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .list-container {
      height: 300px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 10px;
    }
    
    .list-item {
      padding: 10px;
      margin: 5px 0;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 3px solid #007bff;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.2s ease;
    }
    
    .list-item.visible {
      opacity: 1;
      transform: translateY(0);
    }
    
    .performance-summary {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .performance-chart {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    
    .chart-item {
      text-align: center;
      padding: 15px;
      border-radius: 8px;
    }
    
    .chart-item.blocking {
      background: #ffebee;
      border: 2px solid #f44336;
    }
    
    .chart-item.non-blocking {
      background: #e8f5e8;
      border: 2px solid #4caf50;
    }
    
    .chart-value {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .chart-label {
      font-size: 14px;
      color: #666;
    }
    
    @media (max-width: 768px) {
      .comparison-container {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>⚡ useDefer 性能对比演示</h1>
    <p>对比传统一次性渲染与 useDefer 分帧渲染的性能差异</p>
  </div>

  <div class="comparison-container">
    <!-- 传统渲染方式 -->
    <div class="demo-section without-defer">
      <h3>🐌 传统一次性渲染</h3>
      <div class="controls">
        <button onclick="startTraditionalRender(500)">渲染 500 个</button>
        <button onclick="startTraditionalRender(1000)">渲染 1000 个</button>
        <button onclick="startTraditionalRender(2000)">渲染 2000 个</button>
        <button onclick="clearTraditional()">清空</button>
      </div>
      <div class="stats" id="traditionalStats">
        渲染时间: 0ms | 阻塞时间: 0ms | 元素数: 0
      </div>
      <div class="list-container" id="traditionalContainer">
        <p style="text-align: center; color: #666;">点击按钮开始传统渲染</p>
      </div>
    </div>

    <!-- useDefer 渲染方式 -->
    <div class="demo-section with-defer">
      <h3>🚀 useDefer 分帧渲染</h3>
      <div class="controls">
        <button onclick="startDeferRender(500)">渲染 500 个</button>
        <button onclick="startDeferRender(1000)">渲染 1000 个</button>
        <button onclick="startDeferRender(2000)">渲染 2000 个</button>
        <button onclick="clearDefer()">清空</button>
      </div>
      <div class="stats" id="deferStats">
        渲染时间: 0ms | 当前帧: 0 | 已渲染: 0 | 总数: 0
      </div>
      <div class="list-container" id="deferContainer">
        <p style="text-align: center; color: #666;">点击按钮开始分帧渲染</p>
      </div>
    </div>
  </div>

  <div class="performance-summary">
    <h3>📊 性能对比总结</h3>
    <div class="performance-chart" id="performanceChart">
      <div class="chart-item blocking">
        <div class="chart-value" id="blockingTime">0ms</div>
        <div class="chart-label">传统渲染阻塞时间</div>
      </div>
      <div class="chart-item non-blocking">
        <div class="chart-value" id="nonBlockingTime">0ms</div>
        <div class="chart-label">分帧渲染总时间</div>
      </div>
      <div class="chart-item blocking">
        <div class="chart-value" id="blockingFPS">60fps</div>
        <div class="chart-label">传统渲染期间FPS</div>
      </div>
      <div class="chart-item non-blocking">
        <div class="chart-value" id="nonBlockingFPS">60fps</div>
        <div class="chart-label">分帧渲染期间FPS</div>
      </div>
    </div>
    
    <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
      <h4>💡 性能优化原理</h4>
      <ul>
        <li><strong>传统渲染：</strong>一次性创建所有DOM元素，可能导致长时间阻塞主线程，造成页面卡顿</li>
        <li><strong>分帧渲染：</strong>使用 requestAnimationFrame 将渲染任务分散到多个帧中，保持页面流畅</li>
        <li><strong>用户体验：</strong>分帧渲染虽然总时间可能更长，但用户感知的卡顿明显减少</li>
        <li><strong>适用场景：</strong>大量DOM元素、复杂列表、数据可视化等场景</li>
      </ul>
    </div>
  </div>

  <script type="module">
    // useDefer 实现（在实际项目中从 @nuas/utils 导入）
    function useDefer() {
      let count = 1;
      let isRunning = true;
      let rafId = null;
      
      function update() {
        count++;
        if (isRunning) {
          rafId = requestAnimationFrame(update);
        }
      }
      
      rafId = requestAnimationFrame(update);
      
      function defer(n) {
        return count >= n;
      }
      
      function getCurrentFrame() {
        return count;
      }
      
      function cleanup() {
        isRunning = false;
        if (rafId !== null) {
          cancelAnimationFrame(rafId);
          rafId = null;
        }
      }
      
      return { defer, getCurrentFrame, cleanup };
    }

    // 全局变量
    let deferInstance = null;
    let deferAnimationId = null;
    let performanceData = {
      traditional: { time: 0, fps: 60 },
      defer: { time: 0, fps: 60 }
    };

    // 生成测试数据
    function generateItems(count) {
      return Array.from({ length: count }, (_, i) => ({
        id: i,
        title: `列表项 ${i + 1}`,
        description: `这是第 ${i + 1} 个列表项的描述内容，用于性能测试。`
      }));
    }

    // 传统一次性渲染
    window.startTraditionalRender = function(count) {
      const container = document.getElementById('traditionalContainer');
      const startTime = performance.now();
      
      // 清空容器
      container.innerHTML = '';
      
      // 生成数据
      const items = generateItems(count);
      
      // 一次性创建所有元素
      items.forEach((item, index) => {
        const itemEl = document.createElement('div');
        itemEl.className = 'list-item visible';
        itemEl.innerHTML = `
          <h4>${item.title}</h4>
          <p>${item.description}</p>
        `;
        container.appendChild(itemEl);
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // 更新统计信息
      performanceData.traditional = { time: renderTime, fps: 60 };
      document.getElementById('traditionalStats').textContent = 
        `渲染时间: ${renderTime.toFixed(2)}ms | 阻塞时间: ${renderTime.toFixed(2)}ms | 元素数: ${count}`;
      
      updatePerformanceChart();
    };

    // useDefer 分帧渲染
    window.startDeferRender = function(count) {
      // 清理之前的实例
      if (deferInstance) {
        deferInstance.cleanup();
      }
      if (deferAnimationId) {
        cancelAnimationFrame(deferAnimationId);
      }
      
      const container = document.getElementById('deferContainer');
      const startTime = performance.now();
      
      // 清空容器
      container.innerHTML = '';
      
      // 生成数据
      const items = generateItems(count);
      
      // 预创建所有元素（不可见）
      items.forEach((item, index) => {
        const itemEl = document.createElement('div');
        itemEl.className = 'list-item';
        itemEl.dataset.index = index;
        itemEl.innerHTML = `
          <h4>${item.title}</h4>
          <p>${item.description}</p>
        `;
        container.appendChild(itemEl);
      });
      
      // 创建 defer 实例
      deferInstance = useDefer();
      
      // 开始分帧渲染
      function renderLoop() {
        const elements = document.querySelectorAll('#deferContainer .list-item');
        let visibleCount = 0;
        
        elements.forEach(el => {
          const index = parseInt(el.dataset.index);
          if (deferInstance.defer(index)) {
            if (!el.classList.contains('visible')) {
              el.classList.add('visible');
            }
            visibleCount++;
          }
        });
        
        // 更新统计信息
        const currentTime = performance.now() - startTime;
        document.getElementById('deferStats').textContent = 
          `渲染时间: ${currentTime.toFixed(2)}ms | 当前帧: ${deferInstance.getCurrentFrame()} | 已渲染: ${visibleCount} | 总数: ${count}`;
        
        // 如果还有元素未渲染，继续循环
        if (visibleCount < count) {
          deferAnimationId = requestAnimationFrame(renderLoop);
        } else {
          const endTime = performance.now();
          const totalTime = endTime - startTime;
          
          performanceData.defer = { time: totalTime, fps: 60 };
          updatePerformanceChart();
        }
      }
      
      renderLoop();
    };

    // 清空传统渲染
    window.clearTraditional = function() {
      document.getElementById('traditionalContainer').innerHTML = 
        '<p style="text-align: center; color: #666;">点击按钮开始传统渲染</p>';
      document.getElementById('traditionalStats').textContent = 
        '渲染时间: 0ms | 阻塞时间: 0ms | 元素数: 0';
    };

    // 清空 defer 渲染
    window.clearDefer = function() {
      if (deferInstance) {
        deferInstance.cleanup();
        deferInstance = null;
      }
      if (deferAnimationId) {
        cancelAnimationFrame(deferAnimationId);
        deferAnimationId = null;
      }
      document.getElementById('deferContainer').innerHTML = 
        '<p style="text-align: center; color: #666;">点击按钮开始分帧渲染</p>';
      document.getElementById('deferStats').textContent = 
        '渲染时间: 0ms | 当前帧: 0 | 已渲染: 0 | 总数: 0';
    };

    // 更新性能对比图表
    function updatePerformanceChart() {
      document.getElementById('blockingTime').textContent = 
        `${performanceData.traditional.time.toFixed(2)}ms`;
      document.getElementById('nonBlockingTime').textContent = 
        `${performanceData.defer.time.toFixed(2)}ms`;
      document.getElementById('blockingFPS').textContent = 
        `${performanceData.traditional.fps}fps`;
      document.getElementById('nonBlockingFPS').textContent = 
        `${performanceData.defer.fps}fps`;
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
      if (deferInstance) {
        deferInstance.cleanup();
      }
      if (deferAnimationId) {
        cancelAnimationFrame(deferAnimationId);
      }
    });
  </script>
</body>
</html>
