/**
 * 数字工具函数
 * @module core/number
 */

/**
 * 类型保护：检查一个值是否为数字
 * @param value - 要检查的值
 * @returns 如果值为数字则为 true
 * @example
 * ```typescript
 * isNumber(42) // true
 * isNumber('42') // false
 * isNumber(NaN) // true (NaN 的类型是 number)
 * ```
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number';
}

/**
 * 类型保护：检查一个值是否为有限数字（排除 NaN 和 Infinity）
 * @param value - 要检查的值
 * @returns 如果值为有限数字则为 true
 * @example
 * ```typescript
 * isFiniteNumber(42) // true
 * isFiniteNumber(NaN) // false
 * isFiniteNumber(Infinity) // false
 * ```
 */
export function isFiniteNumber(value: unknown): value is number {
  return typeof value === 'number' && Number.isFinite(value);
}

/**
 * 类型保护：检查一个值是否为整数
 * @param value - 要检查的值
 * @returns 如果值为整数则为 true
 * @example
 * ```typescript
 * isInteger(42) // true
 * isInteger(42.5) // false
 * isInteger('42') // false
 * ```
 */
export function isInteger(value: unknown): value is number {
  return typeof value === 'number' && Number.isInteger(value);
}

/**
 * 类型保护：检查一个值是否为数字字符串
 * @param value - 要检查的值
 * @returns 如果值为表示数字的字符串则为 true
 * @example
 * ```typescript
 * isNumericString('42') // true
 * isNumericString('42.5') // true
 * isNumericString('abc') // false
 * ```
 */
export function isNumericString(value: unknown): value is string {
  return typeof value === 'string' && value.trim() !== '' && !Number.isNaN(Number(value));
}

/**
 * 检查一个值是否可以转换为数字
 * @param value - 要检查的值
 * @returns 如果值可以转换为有效数字则为 true
 * @example
 * ```typescript
 * isNumeric(42) // true
 * isNumeric('42') // true
 * isNumeric('abc') // false
 * ```
 */
export function isNumeric(value: unknown): boolean {
  if (isNumber(value)) {
    return !Number.isNaN(value);
  }
  if (isNumericString(value)) {
    return true;
  }
  return false;
}

/**
 * 安全地将任意值转换为数字，带有回退值
 * @param value - 要转换的值
 * @param fallback - 转换失败时的回退值（默认：0）
 * @returns 转换后的数字或回退值
 * @example
 * ```typescript
 * toNumber(3.2) // 3.2
 * toNumber('3.2') // 3.2
 * toNumber('abc') // 0
 * toNumber('abc', -1) // -1
 * toNumber(null) // 0
 * toNumber(undefined) // 0
 * toNumber(true) // 1
 * toNumber(false) // 0
 * toNumber('') // 0
 * toNumber(' ') // 0
 * toNumber(' 3.2 ') // 3.2
 * toNumber('0x12') // 18
 * toNumber('0o12') // 10
 * toNumber('0b10') // 2
 * ```
 */
export function toNumber(value: unknown, fallback = 0): number {
  // 处理 null 和 undefined
  if (value === null || value === undefined) {
    return fallback;
  }

  // 处理布尔值
  if (typeof value === 'boolean') {
    return value ? 1 : 0;
  }

  // 处理数字（包括 NaN 和 Infinity）
  if (typeof value === 'number') {
    return Number.isNaN(value) ? fallback : value;
  }

  // 处理字符串
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (trimmed === '') {
      return fallback;
    }

    // 处理特殊数字格式
    if (/^0x[0-9a-f]+$/i.test(trimmed)) {
      return parseInt(trimmed, 16);
    }
    if (/^0o[0-7]+$/i.test(trimmed)) {
      return parseInt(trimmed.slice(2), 8);
    }
    if (/^0b[01]+$/i.test(trimmed)) {
      return parseInt(trimmed.slice(2), 2);
    }

    const num = Number(trimmed);
    return Number.isNaN(num) ? fallback : num;
  }

  // 处理其他类型
  const num = Number(value);
  return Number.isNaN(num) ? fallback : num;
}

/**
 * 将数字限制在最小值和最大值之间
 * @param value - 要限制的数字
 * @param min - 最小值
 * @param max - 最大值
 * @returns 限制后的数字
 * @example
 * ```typescript
 * clamp(5, 0, 10) // 5
 * clamp(-5, 0, 10) // 0
 * clamp(15, 0, 10) // 10
 * ```
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * 将数字四舍五入到指定的小数位数
 * @param value - 要四舍五入的数字
 * @param decimals - 小数位数（默认：0）
 * @returns 四舍五入后的数字
 * @example
 * ```typescript
 * round(3.14159, 2) // 3.14
 * round(3.14159) // 3
 * round(3.5) // 4
 * ```
 */
export function round(value: number, decimals = 0): number {
  const factor = Math.pow(10, decimals);
  return Math.round(value * factor) / factor;
}

/**
 * 生成一个介于 min 和 max 之间的随机数（包含 min，不包含 max）
 * @param min - 最小值
 * @param max - 最大值
 * @returns 介于 min 和 max 之间的随机数
 * @example
 * ```typescript
 * randomBetween(1, 10) // 1 到 10 之间的随机数
 * randomBetween(0, 1) // 0 到 1 之间的随机数
 * ```
 */
export function randomBetween(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

/**
 * 生成一个介于 min 和 max 之间的随机整数（包含 min 和 max）
 * @param min - 最小值
 * @param max - 最大值
 * @returns 介于 min 和 max 之间的随机整数
 * @example
 * ```typescript
 * randomInt(1, 6) // 1 到 6 之间的随机整数（如骰子）
 * randomInt(0, 100) // 0 到 100 之间的随机整数
 * ```
 */
export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
