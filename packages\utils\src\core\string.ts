/**
 * String utility functions
 * @module core/string
 */

/**
 * Type guard to check if a value is a string
 * @param value - The value to check
 * @returns True if the value is a string
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * Check if a string is empty or contains only whitespace
 * @param value - The string to check
 * @returns True if the string is empty or whitespace-only
 * @example
 * ```typescript
 * isEmpty('') // true
 * isEmpty('   ') // true
 * isEmpty('hello') // false
 * ```
 */
export function isEmpty(value: string): boolean {
  return value.trim().length === 0;
}

/**
 * Capitalize the first letter of a string
 * @param value - The string to capitalize
 * @returns The capitalized string
 * @example
 * ```typescript
 * capitalize('hello') // 'Hello'
 * capitalize('HELLO') // 'HELLO'
 * ```
 */
export function capitalize(value: string): string {
  if (!value) return value;
  return value.charAt(0).toUpperCase() + value.slice(1);
}

/**
 * Convert string to camelCase
 * @param value - The string to convert
 * @returns The camelCase string
 * @example
 * ```typescript
 * camelCase('hello-world') // 'helloWorld'
 * camelCase('hello_world') // 'helloWorld'
 * camelCase('hello world') // 'helloWorld'
 * ```
 */
export function camelCase(value: string): string {
  return value
    .replace(/[-_\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))
    .replace(/^[A-Z]/, (char) => char.toLowerCase());
}

/**
 * Convert string to kebab-case
 * @param value - The string to convert
 * @returns The kebab-case string
 * @example
 * ```typescript
 * kebabCase('helloWorld') // 'hello-world'
 * kebabCase('HelloWorld') // 'hello-world'
 * kebabCase('hello_world') // 'hello-world'
 * ```
 */
export function kebabCase(value: string): string {
  return value
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

/**
 * Convert string to snake_case
 * @param value - The string to convert
 * @returns The snake_case string
 * @example
 * ```typescript
 * snakeCase('helloWorld') // 'hello_world'
 * snakeCase('HelloWorld') // 'hello_world'
 * snakeCase('hello-world') // 'hello_world'
 * ```
 */
export function snakeCase(value: string): string {
  return value
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[-\s]+/g, '_')
    .toLowerCase();
}

/**
 * Truncate a string to a specified length with ellipsis
 * @param value - The string to truncate
 * @param length - The maximum length
 * @param suffix - The suffix to append (default: '...')
 * @returns The truncated string
 * @example
 * ```typescript
 * truncate('Hello World', 5) // 'Hello...'
 * truncate('Hello World', 5, '***') // 'Hello***'
 * truncate('Hi', 5) // 'Hi'
 * ```
 */
export function truncate(value: string, length: number, suffix = '...'): string {
  if (value.length <= length) return value;
  return value.slice(0, length - suffix.length) + suffix;
}

/**
 * Remove HTML tags from a string
 * @param value - The string containing HTML
 * @returns The string without HTML tags
 * @example
 * ```typescript
 * stripHtml('<p>Hello <strong>World</strong></p>') // 'Hello World'
 * stripHtml('<div>Test</div>') // 'Test'
 * ```
 */
export function stripHtml(value: string): string {
  return value.replace(/<[^>]*>/g, '');
}

/**
 * Escape HTML special characters
 * @param value - The string to escape
 * @returns The escaped string
 * @example
 * ```typescript
 * escapeHtml('<script>alert("xss")</script>') // '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;'
 * ```
 */
export function escapeHtml(value: string): string {
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
  };
  
  return value.replace(/[&<>"']/g, (char) => htmlEscapes[char]);
}

/**
 * Generate a random string of specified length
 * @param length - The length of the string
 * @param charset - The character set to use (default: alphanumeric)
 * @returns A random string
 * @example
 * ```typescript
 * randomString(8) // 'aB3xY9mN'
 * randomString(4, 'abc') // 'abca'
 * ```
 */
export function randomString(
  length: number,
  charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
): string {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
}

/**
 * Count the number of words in a string
 * @param value - The string to count words in
 * @returns The number of words
 * @example
 * ```typescript
 * wordCount('Hello world') // 2
 * wordCount('  Hello   world  ') // 2
 * wordCount('') // 0
 * ```
 */
export function wordCount(value: string): number {
  return value.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Reverse a string
 * @param value - The string to reverse
 * @returns The reversed string
 * @example
 * ```typescript
 * reverse('hello') // 'olleh'
 * reverse('12345') // '54321'
 * ```
 */
export function reverse(value: string): string {
  return value.split('').reverse().join('');
}

/**
 * Check if a string is a palindrome
 * @param value - The string to check
 * @param caseSensitive - Whether to consider case (default: false)
 * @returns True if the string is a palindrome
 * @example
 * ```typescript
 * isPalindrome('racecar') // true
 * isPalindrome('RaceCar') // true
 * isPalindrome('RaceCar', true) // false
 * isPalindrome('hello') // false
 * ```
 */
export function isPalindrome(value: string, caseSensitive = false): boolean {
  const normalized = caseSensitive ? value : value.toLowerCase();
  return normalized === reverse(normalized);
}
