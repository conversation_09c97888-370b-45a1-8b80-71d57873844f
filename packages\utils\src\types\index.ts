/**
 * Type definitions and utilities
 * @module types
 */

// Re-export WeChat types
export * from './wechat';

/**
 * Common utility types
 */

/**
 * Make all properties optional recursively
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Make all properties required recursively
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * Make specific properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Make specific properties required
 */
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Extract function parameters as tuple
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never;

/**
 * Extract function return type
 */
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any;

/**
 * Extract Promise resolved type
 */
export type Awaited<T> = T extends PromiseLike<infer U> ? U : T;

/**
 * Create a type with specific keys from another type
 */
export type PickByType<T, U> = {
  [K in keyof T as T[K] extends U ? K : never]: T[K];
};

/**
 * Exclude specific keys by type
 */
export type OmitByType<T, U> = {
  [K in keyof T as T[K] extends U ? never : K]: T[K];
};

/**
 * Get all possible paths of an object as string literals
 */
export type Paths<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? K | `${K}.${Paths<T[K]>}`
          : K
        : never;
    }[keyof T]
  : never;

/**
 * Get the type of a nested property by path
 */
export type PathValue<T, P extends Paths<T>> = P extends `${infer K}.${infer Rest}`
  ? K extends keyof T
    ? Rest extends Paths<T[K]>
      ? PathValue<T[K], Rest>
      : never
    : never
  : P extends keyof T
  ? T[P]
  : never;

/**
 * Create a union of all possible values in an object
 */
export type ValueOf<T> = T[keyof T];

/**
 * Create a type that represents a constructor function
 */
export type Constructor<T = {}> = new (...args: any[]) => T;

/**
 * Create a type that represents a class
 */
export type Class<T = {}> = Constructor<T> & { prototype: T };

/**
 * Create a type for event handler functions
 */
export type EventHandler<T = Event> = (event: T) => void;

/**
 * Create a type for async event handler functions
 */
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;

/**
 * Create a type for callback functions with error handling
 */
export type Callback<T = any, E = Error> = (error: E | null, result?: T) => void;

/**
 * Create a type for async functions
 */
export type AsyncFunction<T extends any[] = any[], R = any> = (...args: T) => Promise<R>;

/**
 * Create a type for functions that can be sync or async
 */
export type MaybeAsync<T extends any[] = any[], R = any> = (...args: T) => R | Promise<R>;

/**
 * Create a type for nullable values
 */
export type Nullable<T> = T | null;

/**
 * Create a type for optional values
 */
export type Optional<T> = T | undefined;

/**
 * Create a type for values that can be null or undefined
 */
export type Maybe<T> = T | null | undefined;

/**
 * Create a type for non-nullable values
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

/**
 * Create a type for array elements
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

/**
 * Create a type for tuple to union
 */
export type TupleToUnion<T extends readonly any[]> = T[number];

/**
 * Create a type for union to intersection
 */
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;

/**
 * Create a type for JSON serializable values
 */
export type JsonValue = string | number | boolean | null | JsonObject | JsonArray;

/**
 * Create a type for JSON objects
 */
export interface JsonObject {
  [key: string]: JsonValue;
}

/**
 * Create a type for JSON arrays
 */
export interface JsonArray extends Array<JsonValue> {}

/**
 * Create a type for serializable objects
 */
export type Serializable = JsonValue;

/**
 * Create a type for deep readonly
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * Create a type for mutable (opposite of readonly)
 */
export type Mutable<T> = {
  -readonly [P in keyof T]: T[P];
};

/**
 * Create a type for deep mutable
 */
export type DeepMutable<T> = {
  -readonly [P in keyof T]: T[P] extends object ? DeepMutable<T[P]> : T[P];
};

/**
 * Brand type for creating nominal types
 */
export type Brand<T, B> = T & { __brand: B };

/**
 * Create a type for branded strings
 */
export type BrandedString<B> = Brand<string, B>;

/**
 * Create a type for branded numbers
 */
export type BrandedNumber<B> = Brand<number, B>;

/**
 * Common branded types
 */
export type UserId = BrandedString<'UserId'>;
export type Email = BrandedString<'Email'>;
export type Url = BrandedString<'Url'>;
export type Timestamp = BrandedNumber<'Timestamp'>;

/**
 * Utility type for creating strict object types
 */
export type Exact<T, U extends T> = T & Record<Exclude<keyof U, keyof T>, never>;

/**
 * Utility type for creating a type with all properties as functions
 */
export type Functionalize<T> = {
  [K in keyof T]: () => T[K];
};

/**
 * Utility type for creating a type with all properties as promises
 */
export type Promisify<T> = {
  [K in keyof T]: Promise<T[K]>;
};

/**
 * Utility type for creating a type with all properties as observables
 */
export type Observify<T> = {
  [K in keyof T]: Observable<T[K]>;
};

/**
 * Simple observable interface
 */
export interface Observable<T> {
  subscribe(observer: (value: T) => void): () => void;
}

/**
 * Utility type for creating environment-specific configurations
 */
export interface EnvironmentConfig {
  development: boolean;
  production: boolean;
  test: boolean;
  [key: string]: any;
}
