<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>useDefer 基础使用示例</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background: #f8f9fa;
    }
    
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .demo-container {
      background: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .controls {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }
    
    button {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      background: #007bff;
      color: white;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }
    
    button:hover {
      background: #0056b3;
      transform: translateY(-1px);
    }
    
    button:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
    }
    
    .stats {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }
    
    .stat-item {
      text-align: center;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      display: block;
    }
    
    .stat-label {
      font-size: 12px;
      opacity: 0.9;
    }
    
    .list-container {
      max-height: 500px;
      overflow-y: auto;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 10px;
    }
    
    .list-item {
      padding: 15px;
      margin: 8px 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 8px;
      border-left: 4px solid #007bff;
      opacity: 0;
      transform: translateX(-20px);
      transition: all 0.3s ease;
    }
    
    .list-item.visible {
      opacity: 1;
      transform: translateX(0);
    }
    
    .list-item h4 {
      margin: 0 0 8px 0;
      color: #495057;
      font-size: 16px;
    }
    
    .list-item p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .performance-info {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }
    
    .performance-info h4 {
      color: #155724;
      margin: 0 0 10px 0;
    }
    
    .performance-info p {
      color: #155724;
      margin: 5px 0;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🚀 useDefer 基础使用示例</h1>
    <p>演示如何使用 @nuas/utils 中的 useDefer 函数优化大量元素的渲染性能</p>
  </div>

  <div class="demo-container">
    <h2>分帧渲染演示</h2>
    
    <div class="controls">
      <button onclick="startDemo(100)">渲染 100 个元素</button>
      <button onclick="startDemo(500)">渲染 500 个元素</button>
      <button onclick="startDemo(1000)">渲染 1000 个元素</button>
      <button onclick="pauseDemo()">暂停</button>
      <button onclick="resumeDemo()">恢复</button>
      <button onclick="resetDemo()">重置</button>
    </div>
    
    <div class="stats" id="stats">
      <div class="stat-item">
        <span class="stat-value" id="frameCount">0</span>
        <span class="stat-label">当前帧数</span>
      </div>
      <div class="stat-item">
        <span class="stat-value" id="visibleCount">0</span>
        <span class="stat-label">已渲染元素</span>
      </div>
      <div class="stat-item">
        <span class="stat-value" id="totalCount">0</span>
        <span class="stat-label">总元素数</span>
      </div>
      <div class="stat-item">
        <span class="stat-value" id="progress">0%</span>
        <span class="stat-label">渲染进度</span>
      </div>
    </div>
    
    <div class="list-container" id="listContainer">
      <p style="text-align: center; color: #6c757d;">点击上方按钮开始演示</p>
    </div>
    
    <div class="performance-info">
      <h4>💡 性能优化说明</h4>
      <p><strong>分帧渲染原理：</strong>useDefer 通过 requestAnimationFrame 将大量元素的渲染分散到多个帧中，避免阻塞主线程。</p>
      <p><strong>适用场景：</strong>长列表、复杂表格、大量图片、动画序列等需要渲染大量元素的场景。</p>
      <p><strong>性能提升：</strong>减少页面卡顿，提升用户体验，特别是在低性能设备上效果明显。</p>
    </div>
  </div>

  <script type="module">
    // 在实际项目中，你应该这样导入：
    // import { useDefer } from '@nuas/utils';
    
    // 为了演示，这里使用内联实现
    function useDefer() {
      let count = 1;
      let isRunning = true;
      let rafId = null;
      
      function update() {
        count++;
        if (isRunning) {
          rafId = requestAnimationFrame(update);
        }
      }
      
      rafId = requestAnimationFrame(update);
      
      function defer(n) {
        if (typeof n !== 'number' || n < 0) {
          console.warn('useDefer: 参数 n 必须是非负数');
          return false;
        }
        return count >= n;
      }
      
      function getCurrentFrame() {
        return count;
      }
      
      function resetFrameCount(value = 1) {
        count = value;
      }
      
      function pause() {
        isRunning = false;
      }
      
      function resume() {
        if (!isRunning) {
          isRunning = true;
          rafId = requestAnimationFrame(update);
        }
      }
      
      function cleanup() {
        isRunning = false;
        if (rafId !== null) {
          cancelAnimationFrame(rafId);
          rafId = null;
        }
      }
      
      return {
        defer,
        getCurrentFrame,
        resetFrameCount,
        pause,
        resume,
        cleanup,
      };
    }

    // 全局状态
    let deferInstance = null;
    let animationId = null;
    let items = [];
    let startTime = 0;

    // 生成测试数据
    function generateItems(count) {
      return Array.from({ length: count }, (_, i) => ({
        id: i,
        title: `列表项 ${i + 1}`,
        description: `这是第 ${i + 1} 个列表项的描述内容。使用 useDefer 实现分帧渲染，优化大量元素的渲染性能。当前时间戳：${Date.now()}`,
      }));
    }

    // 开始演示
    window.startDemo = function(count = 100) {
      // 清理之前的实例
      cleanup();
      
      // 生成数据
      items = generateItems(count);
      startTime = performance.now();
      
      // 创建新的 defer 实例
      deferInstance = useDefer();
      
      // 清空并预创建所有元素
      const container = document.getElementById('listContainer');
      container.innerHTML = '';
      
      items.forEach((item, index) => {
        const itemEl = document.createElement('div');
        itemEl.className = 'list-item';
        itemEl.dataset.index = index;
        itemEl.innerHTML = `
          <h4>${item.title}</h4>
          <p>${item.description}</p>
        `;
        container.appendChild(itemEl);
      });

      // 开始渲染循环
      renderLoop();
    };

    // 渲染循环
    function renderLoop() {
      if (!deferInstance) return;
      
      const elements = document.querySelectorAll('.list-item');
      let visibleCount = 0;
      
      elements.forEach(el => {
        const index = parseInt(el.dataset.index);
        if (deferInstance.defer(index)) {
          if (!el.classList.contains('visible')) {
            el.classList.add('visible');
          }
          visibleCount++;
        }
      });

      // 更新统计信息
      updateStats(deferInstance.getCurrentFrame(), visibleCount, items.length);

      // 如果还有元素未渲染，继续循环
      if (visibleCount < items.length) {
        animationId = requestAnimationFrame(renderLoop);
      } else {
        const endTime = performance.now();
        console.log(`渲染完成！总耗时：${(endTime - startTime).toFixed(2)}ms`);
      }
    }

    // 更新统计信息
    function updateStats(frame, visible, total) {
      document.getElementById('frameCount').textContent = frame;
      document.getElementById('visibleCount').textContent = visible;
      document.getElementById('totalCount').textContent = total;
      document.getElementById('progress').textContent = 
        total > 0 ? `${Math.round((visible / total) * 100)}%` : '0%';
    }

    // 暂停演示
    window.pauseDemo = function() {
      if (deferInstance) {
        deferInstance.pause();
      }
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
    };

    // 恢复演示
    window.resumeDemo = function() {
      if (deferInstance) {
        deferInstance.resume();
        renderLoop();
      }
    };

    // 重置演示
    window.resetDemo = function() {
      cleanup();
      document.getElementById('listContainer').innerHTML = 
        '<p style="text-align: center; color: #6c757d;">点击上方按钮开始演示</p>';
      updateStats(0, 0, 0);
    };

    // 清理函数
    function cleanup() {
      if (deferInstance) {
        deferInstance.cleanup();
        deferInstance = null;
      }
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);
  </script>
</body>
</html>
