/**
 * WeChat Mini Program and Browser API type definitions
 * @module types/wechat
 */

declare namespace WechatMiniprogram {
  interface Wx {
    getEnv(): string;
    miniProgram: {
      getEnv(callback: (res: { miniprogram: boolean }) => void): void;
      navigateTo(options: {
        url: string;
        extraData?: Record<string, any>;
        envVersion?: 'develop' | 'trial' | 'release';
        success?: () => void;
        fail?: (error: any) => void;
        complete?: () => void;
      }): void;
      postMessage(options: { data: Record<string, any> }): void;
    };
  }
}

declare const wx: WechatMiniprogram.Wx;

declare const WeixinJSBridge: {
  on(event: string | 'onPageStateChange', callback: (res: any) => void): void;
  invoke(api: string, params: any, callback: (res: any) => void): void;
  call(api: string, params?: any): void;
};

/**
 * WeChat environment detection result
 */
export type WechatEnvironment = 'wechat' | 'miniprogram' | 'unknown';

/**
 * Page state change event data
 */
export interface PageStateChangeData {
  active: boolean;
  [key: string]: any;
}

/**
 * WeChat Mini Program navigation options
 */
export interface MiniProgramNavigateOptions {
  url: string;
  extraData?: Record<string, any>;
  envVersion?: 'develop' | 'trial' | 'release';
  success?: () => void;
  fail?: (error: any) => void;
  complete?: () => void;
}

/**
 * WeChat browser capabilities
 */
export interface WechatCapabilities {
  canUseWxPay: boolean;
  canUseJSSDK: boolean;
  canNavigateToMiniProgram: boolean;
  canPostMessage: boolean;
  version: string | null;
}
