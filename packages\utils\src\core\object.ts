/**
 * Object utility functions
 * @module core/object
 */

/**
 * Type guard to check if a value is a plain object
 * @param value - The value to check
 * @returns True if the value is a plain object
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * Type guard to check if a value is a plain object (not null, array, or other object types)
 * @param value - The value to check
 * @returns True if the value is a plain object
 */
export function isPlainObject(value: unknown): value is Record<string, unknown> {
  if (!isObject(value)) return false;
  
  // Check if it's a plain object (not a class instance, Date, etc.)
  const proto = Object.getPrototypeOf(value);
  return proto === null || proto === Object.prototype;
}

/**
 * Deep clone an object
 * @param obj - The object to clone
 * @returns A deep copy of the object
 * @example
 * ```typescript
 * const original = { a: 1, b: { c: 2 } };
 * const cloned = deepClone(original);
 * cloned.b.c = 3;
 * console.log(original.b.c); // 2 (unchanged)
 * ```
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T;
  }

  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }

  return obj;
}

/**
 * Deep merge multiple objects
 * @param target - The target object
 * @param sources - The source objects to merge
 * @returns The merged object
 * @example
 * ```typescript
 * const obj1 = { a: 1, b: { c: 2 } };
 * const obj2 = { b: { d: 3 }, e: 4 };
 * deepMerge(obj1, obj2); // { a: 1, b: { c: 2, d: 3 }, e: 4 }
 * ```
 */
export function deepMerge<T extends Record<string, unknown>>(
  target: T,
  ...sources: Partial<T>[]
): T {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isPlainObject(target) && isPlainObject(source)) {
    for (const key in source) {
      if (isPlainObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        deepMerge(target[key] as Record<string, unknown>, source[key] as Record<string, unknown>);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }

  return deepMerge(target, ...sources);
}

/**
 * Get a nested property value using dot notation
 * @param obj - The object to get the value from
 * @param path - The path to the property (e.g., 'a.b.c')
 * @param defaultValue - The default value if the property doesn't exist
 * @returns The property value or default value
 * @example
 * ```typescript
 * const obj = { a: { b: { c: 42 } } };
 * get(obj, 'a.b.c') // 42
 * get(obj, 'a.b.d', 'default') // 'default'
 * ```
 */
export function get<T = unknown>(
  obj: Record<string, unknown>,
  path: string,
  defaultValue?: T,
): T {
  const keys = path.split('.');
  let result: unknown = obj;

  for (const key of keys) {
    if (result === null || result === undefined || typeof result !== 'object') {
      return defaultValue as T;
    }
    result = (result as Record<string, unknown>)[key];
  }

  return result === undefined ? (defaultValue as T) : (result as T);
}

/**
 * Set a nested property value using dot notation
 * @param obj - The object to set the value on
 * @param path - The path to the property (e.g., 'a.b.c')
 * @param value - The value to set
 * @returns The modified object
 * @example
 * ```typescript
 * const obj = {};
 * set(obj, 'a.b.c', 42); // { a: { b: { c: 42 } } }
 * ```
 */
export function set<T extends Record<string, unknown>>(
  obj: T,
  path: string,
  value: unknown,
): T {
  const keys = path.split('.');
  let current: Record<string, unknown> = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key] as Record<string, unknown>;
  }

  current[keys[keys.length - 1]] = value;
  return obj;
}

/**
 * Check if an object has a nested property using dot notation
 * @param obj - The object to check
 * @param path - The path to the property
 * @returns True if the property exists
 * @example
 * ```typescript
 * const obj = { a: { b: { c: 42 } } };
 * has(obj, 'a.b.c') // true
 * has(obj, 'a.b.d') // false
 * ```
 */
export function has(obj: Record<string, unknown>, path: string): boolean {
  const keys = path.split('.');
  let current: unknown = obj;

  for (const key of keys) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return false;
    }
    if (!(key in (current as Record<string, unknown>))) {
      return false;
    }
    current = (current as Record<string, unknown>)[key];
  }

  return true;
}

/**
 * Pick specific properties from an object
 * @param obj - The source object
 * @param keys - The keys to pick
 * @returns A new object with only the picked properties
 * @example
 * ```typescript
 * const obj = { a: 1, b: 2, c: 3 };
 * pick(obj, ['a', 'c']) // { a: 1, c: 3 }
 * ```
 */
export function pick<T extends Record<string, unknown>, K extends keyof T>(
  obj: T,
  keys: K[],
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }
  return result;
}

/**
 * Omit specific properties from an object
 * @param obj - The source object
 * @param keys - The keys to omit
 * @returns A new object without the omitted properties
 * @example
 * ```typescript
 * const obj = { a: 1, b: 2, c: 3 };
 * omit(obj, ['b']) // { a: 1, c: 3 }
 * ```
 */
export function omit<T extends Record<string, unknown>, K extends keyof T>(
  obj: T,
  keys: K[],
): Omit<T, K> {
  const result = { ...obj };
  for (const key of keys) {
    delete result[key];
  }
  return result;
}

/**
 * Get all keys of an object (including nested keys with dot notation)
 * @param obj - The object to get keys from
 * @param prefix - Internal prefix for recursion
 * @returns An array of all keys
 * @example
 * ```typescript
 * const obj = { a: 1, b: { c: 2, d: { e: 3 } } };
 * getAllKeys(obj) // ['a', 'b.c', 'b.d.e']
 * ```
 */
export function getAllKeys(obj: Record<string, unknown>, prefix = ''): string[] {
  const keys: string[] = [];
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      keys.push(fullKey);
      
      if (isPlainObject(obj[key])) {
        keys.push(...getAllKeys(obj[key] as Record<string, unknown>, fullKey));
      }
    }
  }
  
  return keys;
}

/**
 * Check if an object is empty
 * @param obj - The object to check
 * @returns True if the object has no enumerable properties
 */
export function isEmpty(obj: Record<string, unknown>): boolean {
  return Object.keys(obj).length === 0;
}
