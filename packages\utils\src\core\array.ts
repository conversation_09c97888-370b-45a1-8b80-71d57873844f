/**
 * Array utility functions
 * @module core/array
 */

/**
 * Type guard to check if a value is an array
 * @param value - The value to check
 * @returns True if the value is an array
 */
export function isArray<T = unknown>(value: unknown): value is T[] {
  return Array.isArray(value);
}

/**
 * Remove duplicate values from an array
 * @param array - The array to deduplicate
 * @returns A new array with unique values
 * @example
 * ```typescript
 * unique([1, 2, 2, 3, 3, 3]) // [1, 2, 3]
 * unique(['a', 'b', 'a', 'c']) // ['a', 'b', 'c']
 * ```
 */
export function unique<T>(array: T[]): T[] {
  return [...new Set(array)];
}

/**
 * Remove duplicate objects from an array based on a key
 * @param array - The array of objects
 * @param key - The key to compare
 * @returns A new array with unique objects
 * @example
 * ```typescript
 * const users = [
 *   { id: 1, name: '<PERSON>' },
 *   { id: 2, name: '<PERSON>' },
 *   { id: 1, name: '<PERSON>' }
 * ];
 * uniqueBy(users, 'id') // [{ id: 1, name: '<PERSON>' }, { id: 2, name: '<PERSON>' }]
 * ```
 */
export function uniqueBy<T, K extends keyof T>(array: T[], key: K): T[] {
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
}

/**
 * Chunk an array into smaller arrays of specified size
 * @param array - The array to chunk
 * @param size - The size of each chunk
 * @returns An array of chunks
 * @example
 * ```typescript
 * chunk([1, 2, 3, 4, 5], 2) // [[1, 2], [3, 4], [5]]
 * chunk(['a', 'b', 'c', 'd'], 3) // [['a', 'b', 'c'], ['d']]
 * ```
 */
export function chunk<T>(array: T[], size: number): T[][] {
  if (size <= 0) return [];
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * Flatten a nested array to a specified depth
 * @param array - The array to flatten
 * @param depth - The depth to flatten (default: 1)
 * @returns A flattened array
 * @example
 * ```typescript
 * flatten([1, [2, 3], [4, [5]]]) // [1, 2, 3, 4, [5]]
 * flatten([1, [2, 3], [4, [5]]], 2) // [1, 2, 3, 4, 5]
 * ```
 */
export function flatten<T>(array: unknown[], depth = 1): T[] {
  return depth > 0
    ? array.reduce<T[]>((acc, val) => 
      acc.concat(Array.isArray(val) ? flatten(val, depth - 1) : val as T), [])
    : array as T[];
}

/**
 * Shuffle an array randomly
 * @param array - The array to shuffle
 * @returns A new shuffled array
 * @example
 * ```typescript
 * shuffle([1, 2, 3, 4, 5]) // [3, 1, 5, 2, 4] (random order)
 * ```
 */
export function shuffle<T>(array: T[]): T[] {
  const result = [...array];
  for (let i = result.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [result[i], result[j]] = [result[j], result[i]];
  }
  return result;
}

/**
 * Get a random element from an array
 * @param array - The array to pick from
 * @returns A random element or undefined if array is empty
 * @example
 * ```typescript
 * sample([1, 2, 3, 4, 5]) // 3 (random)
 * sample([]) // undefined
 * ```
 */
export function sample<T>(array: T[]): T | undefined {
  if (array.length === 0) return undefined;
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Get multiple random elements from an array
 * @param array - The array to pick from
 * @param count - The number of elements to pick
 * @returns An array of random elements
 * @example
 * ```typescript
 * sampleSize([1, 2, 3, 4, 5], 3) // [2, 4, 1] (random)
 * sampleSize([1, 2], 5) // [1, 2] (returns all if count > length)
 * ```
 */
export function sampleSize<T>(array: T[], count: number): T[] {
  if (count >= array.length) return shuffle(array);
  if (count <= 0) return [];
  
  const shuffled = shuffle(array);
  return shuffled.slice(0, count);
}

/**
 * Group array elements by a key
 * @param array - The array to group
 * @param key - The key to group by
 * @returns An object with grouped elements
 * @example
 * ```typescript
 * const users = [
 *   { name: 'John', age: 25 },
 *   { name: 'Jane', age: 30 },
 *   { name: 'Bob', age: 25 }
 * ];
 * groupBy(users, 'age') // { 25: [John, Bob], 30: [Jane] }
 * ```
 */
export function groupBy<T, K extends keyof T>(
  array: T[],
  key: K,
): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const groupKey = String(item[key]);
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

/**
 * Find the intersection of multiple arrays
 * @param arrays - The arrays to intersect
 * @returns An array containing elements present in all arrays
 * @example
 * ```typescript
 * intersection([1, 2, 3], [2, 3, 4], [3, 4, 5]) // [3]
 * intersection(['a', 'b'], ['b', 'c'], ['b', 'd']) // ['b']
 * ```
 */
export function intersection<T>(...arrays: T[][]): T[] {
  if (arrays.length === 0) return [];
  if (arrays.length === 1) return [...arrays[0]];
  
  return arrays.reduce((acc, current) => 
    acc.filter(item => current.includes(item)),
  );
}

/**
 * Find the difference between arrays (elements in first array but not in others)
 * @param array - The base array
 * @param others - Arrays to subtract from the base
 * @returns Elements in the first array but not in others
 * @example
 * ```typescript
 * difference([1, 2, 3, 4], [2, 3], [4]) // [1]
 * difference(['a', 'b', 'c'], ['b']) // ['a', 'c']
 * ```
 */
export function difference<T>(array: T[], ...others: T[][]): T[] {
  const otherItems = new Set(others.flat());
  return array.filter(item => !otherItems.has(item));
}

/**
 * Check if an array is empty
 * @param array - The array to check
 * @returns True if the array is empty
 */
export function isEmpty<T>(array: T[]): boolean {
  return array.length === 0;
}

/**
 * Get the last element of an array
 * @param array - The array
 * @returns The last element or undefined if empty
 */
export function last<T>(array: T[]): T | undefined {
  return array[array.length - 1];
}

/**
 * Get the first element of an array
 * @param array - The array
 * @returns The first element or undefined if empty
 */
export function first<T>(array: T[]): T | undefined {
  return array[0];
}
