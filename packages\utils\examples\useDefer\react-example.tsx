import React, { useState, useEffect, useCallback, useMemo } from 'react';

// 在实际项目中使用：
// import { useDefer, type UseDeferReturn } from '@nuas/utils';

// 为了演示，这里内联类型定义和实现
interface UseDeferReturn {
  defer: (n: number) => boolean;
  getCurrentFrame: () => number;
  resetFrameCount: (value?: number) => void;
  pause: () => void;
  resume: () => void;
  cleanup: () => void;
}

function useDefer(): UseDeferReturn {
  let count = 1;
  let isRunning = true;
  let rafId: number | null = null;
  
  function update(): void {
    count++;
    if (isRunning) {
      rafId = requestAnimationFrame(update);
    }
  }
  
  rafId = requestAnimationFrame(update);
  
  function defer(n: number): boolean {
    if (typeof n !== 'number' || n < 0) {
      console.warn('useDefer: 参数 n 必须是非负数');
      return false;
    }
    return count >= n;
  }
  
  function getCurrentFrame(): number {
    return count;
  }
  
  function resetFrameCount(value = 1): void {
    count = value;
  }
  
  function pause(): void {
    isRunning = false;
  }
  
  function resume(): void {
    if (!isRunning) {
      isRunning = true;
      rafId = requestAnimationFrame(update);
    }
  }
  
  function cleanup(): void {
    isRunning = false;
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  }
  
  return {
    defer,
    getCurrentFrame,
    resetFrameCount,
    pause,
    resume,
    cleanup,
  };
}

interface ListItem {
  id: number;
  title: string;
  description: string;
}

const UseDeferExample: React.FC = () => {
  const [items, setItems] = useState<ListItem[]>([]);
  const [currentFrame, setCurrentFrame] = useState(0);
  const [deferInstance, setDeferInstance] = useState<UseDeferReturn | null>(null);
  const [isRendering, setIsRendering] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [renderTime, setRenderTime] = useState(0);
  const [startTime, setStartTime] = useState(0);

  // 生成测试数据
  const generateItems = useCallback((count: number): ListItem[] => {
    return Array.from({ length: count }, (_, i) => ({
      id: i,
      title: `React 列表项 ${i + 1}`,
      description: `这是第 ${i + 1} 个列表项，使用 React + useDefer 实现分帧渲染。创建时间：${new Date().toLocaleTimeString()}`,
    }));
  }, []);

  // 判断元素是否应该显示
  const shouldShow = useCallback((index: number): boolean => {
    return deferInstance ? deferInstance.defer(index) : false;
  }, [deferInstance]);

  // 计算可见元素数量
  const visibleCount = useMemo(() => {
    if (!deferInstance) return 0;
    return items.filter((_, index) => shouldShow(index)).length;
  }, [items, deferInstance, currentFrame]); // 添加 currentFrame 作为依赖

  // 计算渲染进度
  const progress = useMemo(() => {
    if (items.length === 0) return 0;
    return Math.round((visibleCount / items.length) * 100);
  }, [visibleCount, items.length]);

  // 更新帧数显示
  const updateFrameDisplay = useCallback(() => {
    if (deferInstance) {
      setCurrentFrame(deferInstance.getCurrentFrame());
    }
    
    if (isRendering && !isPaused) {
      requestAnimationFrame(updateFrameDisplay);
    }
  }, [deferInstance, isRendering, isPaused]);

  // 开始渲染
  const startRender = useCallback((count: number) => {
    // 清理之前的实例
    if (deferInstance) {
      deferInstance.cleanup();
    }
    
    // 生成数据
    const newItems = generateItems(count);
    setItems(newItems);
    setStartTime(performance.now());
    setIsRendering(true);
    setIsPaused(false);
    setRenderTime(0);
    
    // 创建新实例
    const newInstance = useDefer();
    setDeferInstance(newInstance);
    
    // 开始更新显示
    setTimeout(updateFrameDisplay, 0);
  }, [deferInstance, generateItems, updateFrameDisplay]);

  // 暂停渲染
  const pauseRender = useCallback(() => {
    if (deferInstance) {
      deferInstance.pause();
      setIsPaused(true);
    }
  }, [deferInstance]);

  // 恢复渲染
  const resumeRender = useCallback(() => {
    if (deferInstance) {
      deferInstance.resume();
      setIsPaused(false);
      updateFrameDisplay();
    }
  }, [deferInstance, updateFrameDisplay]);

  // 重置渲染
  const resetRender = useCallback(() => {
    if (deferInstance) {
      deferInstance.cleanup();
    }
    setDeferInstance(null);
    setItems([]);
    setCurrentFrame(0);
    setIsRendering(false);
    setIsPaused(false);
    setRenderTime(0);
  }, [deferInstance]);

  // 监听渲染完成
  useEffect(() => {
    if (visibleCount > 0 && visibleCount === items.length && isRendering) {
      const endTime = performance.now();
      setRenderTime(endTime - startTime);
      setIsRendering(false);
      setIsPaused(false);
      console.log(`React 渲染完成！总耗时：${(endTime - startTime).toFixed(2)}ms`);
    }
  }, [visibleCount, items.length, isRendering, startTime]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (deferInstance) {
        deferInstance.cleanup();
      }
    };
  }, [deferInstance]);

  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h1>⚛️ React + useDefer 示例</h1>
        <p>演示在 React 中使用 @nuas/utils 的 useDefer 函数</p>
      </div>

      <div style={styles.demoContainer}>
        <div style={styles.controls}>
          <button 
            onClick={() => startRender(100)} 
            disabled={isRendering}
            style={styles.button}
          >
            渲染 100 个元素
          </button>
          <button 
            onClick={() => startRender(500)} 
            disabled={isRendering}
            style={styles.button}
          >
            渲染 500 个元素
          </button>
          <button 
            onClick={() => startRender(1000)} 
            disabled={isRendering}
            style={styles.button}
          >
            渲染 1000 个元素
          </button>
          <button 
            onClick={pauseRender} 
            disabled={!isRendering}
            style={styles.button}
          >
            暂停
          </button>
          <button 
            onClick={resumeRender} 
            disabled={!isPaused}
            style={styles.button}
          >
            恢复
          </button>
          <button 
            onClick={resetRender}
            style={styles.button}
          >
            重置
          </button>
        </div>

        <div style={styles.stats}>
          <div style={styles.statItem}>
            <span style={styles.statValue}>{currentFrame}</span>
            <span style={styles.statLabel}>当前帧数</span>
          </div>
          <div style={styles.statItem}>
            <span style={styles.statValue}>{visibleCount}</span>
            <span style={styles.statLabel}>已渲染元素</span>
          </div>
          <div style={styles.statItem}>
            <span style={styles.statValue}>{items.length}</span>
            <span style={styles.statLabel}>总元素数</span>
          </div>
          <div style={styles.statItem}>
            <span style={styles.statValue}>{progress}%</span>
            <span style={styles.statLabel}>渲染进度</span>
          </div>
        </div>

        <div style={styles.listContainer}>
          {items.map((item, index) => (
            <div
              key={item.id}
              style={{
                ...styles.listItem,
                opacity: shouldShow(index) ? 1 : 0,
                transform: shouldShow(index) ? 'translateY(0)' : 'translateY(20px)',
              }}
            >
              <h4 style={styles.itemTitle}>{item.title}</h4>
              <p style={styles.itemDescription}>{item.description}</p>
              <div style={styles.itemMeta}>
                <span>序号: {index + 1}</span>
                <span>渲染帧: {index + 1}</span>
              </div>
            </div>
          ))}
        </div>

        {renderTime > 0 && (
          <div style={styles.performanceInfo}>
            <h4>📊 性能统计</h4>
            <p><strong>总渲染时间:</strong> {renderTime.toFixed(2)}ms</p>
            <p><strong>平均每帧渲染:</strong> {currentFrame > 0 ? (visibleCount / currentFrame).toFixed(2) : 0} 个元素</p>
            <p><strong>渲染状态:</strong> {
              !isRendering ? '未开始' : 
              isPaused ? '已暂停' : 
              visibleCount === items.length ? '已完成' : '渲染中...'
            }</p>
          </div>
        )}
      </div>
    </div>
  );
};

// 样式定义
const styles: { [key: string]: React.CSSProperties } = {
  container: {
    maxWidth: '1000px',
    margin: '0 auto',
    padding: '20px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  header: {
    textAlign: 'center',
    marginBottom: '30px',
    padding: '20px',
    background: 'linear-gradient(135deg, #61dafb 0%, #21232a 100%)',
    color: 'white',
    borderRadius: '12px',
  },
  demoContainer: {
    background: 'white',
    borderRadius: '12px',
    padding: '20px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  },
  controls: {
    display: 'flex',
    gap: '10px',
    marginBottom: '20px',
    flexWrap: 'wrap',
  },
  button: {
    padding: '10px 20px',
    border: 'none',
    borderRadius: '6px',
    background: '#61dafb',
    color: '#21232a',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold',
    transition: 'all 0.2s',
  },
  stats: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '20px',
    borderRadius: '8px',
    marginBottom: '20px',
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
    gap: '15px',
  },
  statItem: {
    textAlign: 'center',
  },
  statValue: {
    fontSize: '28px',
    fontWeight: 'bold',
    display: 'block',
  },
  statLabel: {
    fontSize: '12px',
    opacity: 0.9,
  },
  listContainer: {
    maxHeight: '500px',
    overflowY: 'auto',
    border: '1px solid #e9ecef',
    borderRadius: '8px',
    padding: '15px',
  },
  listItem: {
    padding: '20px',
    margin: '10px 0',
    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
    borderRadius: '8px',
    borderLeft: '4px solid #61dafb',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.3s ease',
  },
  itemTitle: {
    margin: '0 0 10px 0',
    color: '#2c3e50',
    fontSize: '16px',
  },
  itemDescription: {
    margin: '0 0 10px 0',
    color: '#7f8c8d',
    fontSize: '14px',
    lineHeight: '1.6',
  },
  itemMeta: {
    display: 'flex',
    gap: '15px',
    fontSize: '12px',
    color: '#95a5a6',
  },
  performanceInfo: {
    background: '#d4edda',
    border: '1px solid #c3e6cb',
    borderRadius: '8px',
    padding: '20px',
    marginTop: '20px',
  },
};

export default UseDeferExample;
