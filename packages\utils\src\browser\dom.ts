/**
 * DOM manipulation utilities
 * @module browser/dom
 */

import { isBrowser } from './detection';

/**
 * Safely query a DOM element
 * @param selector - CSS selector
 * @param context - Context element (default: document)
 * @returns The found element or null
 */
export function querySelector<T extends Element = Element>(
  selector: string,
  context: Document | Element = document,
): T | null {
  if (!isBrowser()) return null;
  
  try {
    return context.querySelector<T>(selector);
  } catch {
    return null;
  }
}

/**
 * Safely query multiple DOM elements
 * @param selector - CSS selector
 * @param context - Context element (default: document)
 * @returns Array of found elements
 */
export function querySelectorAll<T extends Element = Element>(
  selector: string,
  context: Document | Element = document,
): T[] {
  if (!isBrowser()) return [];
  
  try {
    return Array.from(context.querySelectorAll<T>(selector));
  } catch {
    return [];
  }
}

/**
 * Get element by ID safely
 * @param id - Element ID
 * @returns The found element or null
 */
export function getElementById<T extends HTMLElement = HTMLElement>(id: string): T | null {
  if (!isBrowser()) return null;
  
  return document.getElementById(id) as T | null;
}

/**
 * Create a DOM element with attributes and content
 * @param tagName - HTML tag name
 * @param attributes - Element attributes
 * @param content - Element content (text or HTML)
 * @returns The created element
 */
export function createElement<K extends keyof HTMLElementTagNameMap>(
  tagName: K,
  attributes?: Record<string, string>,
  content?: string,
): HTMLElementTagNameMap[K] | null {
  if (!isBrowser()) return null;
  
  try {
    const element = document.createElement(tagName);
    
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
    }
    
    if (content !== undefined) {
      element.textContent = content;
    }
    
    return element;
  } catch {
    return null;
  }
}

/**
 * Add CSS class to element
 * @param element - Target element
 * @param className - Class name to add
 */
export function addClass(element: Element, className: string): void {
  if (!element || !className) return;
  
  element.classList.add(className);
}

/**
 * Remove CSS class from element
 * @param element - Target element
 * @param className - Class name to remove
 */
export function removeClass(element: Element, className: string): void {
  if (!element || !className) return;
  
  element.classList.remove(className);
}

/**
 * Toggle CSS class on element
 * @param element - Target element
 * @param className - Class name to toggle
 * @returns True if class was added, false if removed
 */
export function toggleClass(element: Element, className: string): boolean {
  if (!element || !className) return false;
  
  return element.classList.toggle(className);
}

/**
 * Check if element has CSS class
 * @param element - Target element
 * @param className - Class name to check
 * @returns True if element has the class
 */
export function hasClass(element: Element, className: string): boolean {
  if (!element || !className) return false;
  
  return element.classList.contains(className);
}

/**
 * Get element's computed style property
 * @param element - Target element
 * @param property - CSS property name
 * @returns The computed style value
 */
export function getStyle(element: Element, property: string): string {
  if (!isBrowser() || !element) return '';
  
  return window.getComputedStyle(element).getPropertyValue(property);
}

/**
 * Set element's style property
 * @param element - Target element
 * @param property - CSS property name
 * @param value - CSS property value
 */
export function setStyle(element: HTMLElement, property: string, value: string): void {
  if (!element) return;
  
  element.style.setProperty(property, value);
}

/**
 * Set multiple style properties on element
 * @param element - Target element
 * @param styles - Object with CSS properties and values
 */
export function setStyles(element: HTMLElement, styles: Record<string, string>): void {
  if (!element) return;
  
  Object.entries(styles).forEach(([property, value]) => {
    setStyle(element, property, value);
  });
}

/**
 * Get element's offset position relative to document
 * @param element - Target element
 * @returns Object with top and left coordinates
 */
export function getOffset(element: Element): { top: number; left: number } {
  if (!element) return { top: 0, left: 0 };
  
  const rect = element.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  return {
    top: rect.top + scrollTop,
    left: rect.left + scrollLeft,
  };
}

/**
 * Get element's position relative to viewport
 * @param element - Target element
 * @returns DOMRect object with position and size
 */
export function getBoundingRect(element: Element): DOMRect | null {
  if (!element) return null;
  
  return element.getBoundingClientRect();
}

/**
 * Check if element is visible in viewport
 * @param element - Target element
 * @param threshold - Visibility threshold (0-1, default: 0)
 * @returns True if element is visible
 */
export function isElementVisible(element: Element, threshold = 0): boolean {
  if (!element || !isBrowser()) return false;
  
  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;
  
  const verticalVisible = rect.top < windowHeight && rect.bottom > 0;
  const horizontalVisible = rect.left < windowWidth && rect.right > 0;
  
  if (!verticalVisible || !horizontalVisible) return false;
  
  if (threshold === 0) return true;
  
  const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
  const visibleWidth = Math.min(rect.right, windowWidth) - Math.max(rect.left, 0);
  const visibleArea = visibleHeight * visibleWidth;
  const totalArea = rect.height * rect.width;
  
  return visibleArea / totalArea >= threshold;
}

/**
 * Scroll element into view smoothly
 * @param element - Target element
 * @param options - Scroll options
 */
export function scrollIntoView(
  element: Element,
  options: ScrollIntoViewOptions = { behavior: 'smooth', block: 'center' },
): void {
  if (!element) return;
  
  element.scrollIntoView(options);
}

/**
 * Get scroll position of element or window
 * @param element - Target element (default: window)
 * @returns Object with scrollTop and scrollLeft
 */
export function getScrollPosition(element?: Element): { top: number; left: number } {
  if (!isBrowser()) return { top: 0, left: 0 };
  
  if (!element) {
    return {
      top: window.pageYOffset || document.documentElement.scrollTop,
      left: window.pageXOffset || document.documentElement.scrollLeft,
    };
  }
  
  return {
    top: element.scrollTop,
    left: element.scrollLeft,
  };
}

/**
 * Set scroll position of element or window
 * @param options - Scroll options
 */
export function setScrollPosition(options: {
  top?: number;
  left?: number;
  element?: Element;
  behavior?: ScrollBehavior;
}): void {
  if (!isBrowser()) return;
  
  const { top, left, element, behavior = 'auto' } = options;
  
  if (!element) {
    window.scrollTo({
      top,
      left,
      behavior,
    });
  } else {
    if (top !== undefined) element.scrollTop = top;
    if (left !== undefined) element.scrollLeft = left;
  }
}

/**
 * Add event listener with automatic cleanup
 * @param element - Target element
 * @param event - Event name
 * @param handler - Event handler
 * @param options - Event options
 * @returns Cleanup function
 */
export function addEventListener<K extends keyof HTMLElementEventMap>(
  element: Element,
  event: K,
  handler: (event: HTMLElementEventMap[K]) => void,
  options?: AddEventListenerOptions,
): () => void {
  if (!element) return () => {};
  
  element.addEventListener(event, handler as EventListener, options);
  
  return () => {
    element.removeEventListener(event, handler as EventListener, options);
  };
}

/**
 * Wait for DOM content to be loaded
 * @returns Promise that resolves when DOM is ready
 */
export function waitForDOMReady(): Promise<void> {
  if (!isBrowser()) return Promise.resolve();
  
  return new Promise((resolve) => {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => resolve(), { once: true });
    } else {
      resolve();
    }
  });
}

/**
 * Load external script dynamically
 * @param src - Script URL
 * @param options - Load options
 * @returns Promise that resolves when script is loaded
 */
export function loadScript(
  src: string,
  options: {
    async?: boolean;
    defer?: boolean;
    crossOrigin?: string;
    integrity?: string;
  } = {},
): Promise<void> {
  if (!isBrowser()) return Promise.reject(new Error('Not in browser environment'));
  
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.async = options.async ?? true;
    script.defer = options.defer ?? false;
    
    if (options.crossOrigin) script.crossOrigin = options.crossOrigin;
    if (options.integrity) script.integrity = options.integrity;
    
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
    
    document.head.appendChild(script);
  });
}
