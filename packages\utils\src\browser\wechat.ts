/**
 * WeChat-specific browser utilities
 * @module browser/wechat
 */

import { isBrowser } from './detection';

/**
 * Check if running in WeChat browser
 * @returns True if in WeChat browser
 * @example
 * ```typescript
 * if (isWechat()) {
 *   // WeChat-specific functionality
 *   initWechatSDK();
 * }
 * ```
 */
export function isWechat(): boolean {
  if (!isBrowser()) return false;
  
  const userAgent = navigator.userAgent;
  return /MicroMessenger/i.test(userAgent) && !/miniProgram/i.test(userAgent);
}

/**
 * Alias for isWechat for backward compatibility
 * @deprecated Use isWechat instead
 */
export const isWeixin = isWechat;

/**
 * Check if running in WeChat Mini Program
 * @returns True if in WeChat Mini Program
 * @example
 * ```typescript
 * if (isMiniprogram()) {
 *   // Mini Program specific code
 *   wx.showToast({ title: 'Hello Mini Program' });
 * }
 * ```
 */
export function isMiniprogram(): boolean {
  if (!isBrowser()) return false;
  
  // Check for mini program user agent
  if (/miniProgram/i.test(navigator.userAgent)) {
    return true;
  }
  
  // Check for wx object in mini program environment
  if (typeof wx !== 'undefined' && wx.getEnv) {
    try {
      return wx.getEnv() === 'miniProgram';
    } catch {
      return false;
    }
  }
  
  return false;
}

/**
 * WeChat environment types
 */
export type WechatEnvironment = 'wechat' | 'miniprogram' | 'unknown';

/**
 * Get WeChat environment asynchronously
 * @returns Promise resolving to the WeChat environment type
 * @example
 * ```typescript
 * const env = await getWxEnv();
 * if (env === 'miniprogram') {
 *   // Mini program specific logic
 * } else if (env === 'wechat') {
 *   // WeChat browser specific logic
 * }
 * ```
 */
export function getWxEnv(): Promise<WechatEnvironment> {
  return new Promise((resolve) => {
    if (!isBrowser()) {
      resolve('unknown');
      return;
    }
    
    // Quick check for mini program
    if (isMiniprogram()) {
      resolve('miniprogram');
      return;
    }
    
    // Check if in WeChat browser
    if (!isWechat()) {
      resolve('unknown');
      return;
    }
    
    // Try to detect mini program environment using wx.miniProgram
    try {
      let targetWindow: any = window;
      
      // Check current window and parent window for wx object
      if (!targetWindow.wx && window.parent !== window) {
        targetWindow = window.parent;
      }
      
      if (!targetWindow.wx?.miniProgram) {
        resolve('wechat');
        return;
      }
      
      // Use wx.miniProgram.getEnv to detect environment
      targetWindow.wx.miniProgram.getEnv((res: any) => {
        if (res.miniprogram) {
          resolve('miniprogram');
        } else {
          resolve('wechat');
        }
      });
    } catch {
      resolve('wechat');
    }
  });
}

/**
 * Check if running in WeChat Mini Program asynchronously
 * @returns Promise resolving to true if in mini program
 * @example
 * ```typescript
 * const inMiniProgram = await isMiniprogramAsync();
 * if (inMiniProgram) {
 *   // Mini program specific code
 * }
 * ```
 */
export async function isMiniprogramAsync(): Promise<boolean> {
  if (!isWechat()) return false;
  
  const env = await getWxEnv();
  return env === 'miniprogram';
}

/**
 * Page state change event data
 */
export interface PageStateChangeData {
  active: boolean;
  [key: string]: any;
}

/**
 * Listen for WeChat Mini Program page state changes
 * @param callback - Callback function to handle state changes
 * @returns Cleanup function to remove the listener
 * @example
 * ```typescript
 * const cleanup = onPageStateChange((res) => {
 *   if (res.active) {
 *     console.log('Mini program is in foreground');
 *   } else {
 *     console.log('Mini program is in background');
 *   }
 * });
 * 
 * // Later, cleanup the listener
 * cleanup();
 * ```
 */
export function onPageStateChange<T extends PageStateChangeData>(
  callback?: (res: T) => void,
): () => void {
  if (!isBrowser() || !isWechat()) {
    return () => {}; // Return empty cleanup function
  }
  
  const handleStateChange = (res: T) => {
    callback?.(res);
    // Dispatch custom event for other parts of the application
    window.dispatchEvent(
      new CustomEvent('onPageStateChangeMiniprogram', { detail: res }),
    );
  };
  
  // Check if WeixinJSBridge is available
  if (typeof WeixinJSBridge !== 'undefined') {
    WeixinJSBridge.on('onPageStateChange', handleStateChange);
    
    return () => {
      // Note: WeixinJSBridge doesn't provide an off method
      // The listener will be automatically cleaned up when the page unloads
    };
  }
  
  return () => {};
}

/**
 * WeChat Mini Program navigation options
 */
export interface MiniProgramNavigateOptions {
  url: string;
  extraData?: Record<string, any>;
  envVersion?: 'develop' | 'trial' | 'release';
  success?: () => void;
  fail?: (error: any) => void;
  complete?: () => void;
}

/**
 * Navigate to a WeChat Mini Program page
 * @param options - Navigation options
 * @example
 * ```typescript
 * navigateToMiniProgram({
 *   url: 'pages/index/index',
 *   extraData: { from: 'h5' },
 *   success: () => console.log('Navigation successful'),
 *   fail: (error) => console.error('Navigation failed', error)
 * });
 * ```
 */
export function navigateToMiniProgram(options: MiniProgramNavigateOptions): void {
  if (!isBrowser() || !isWechat()) {
    options.fail?.({ message: 'Not in WeChat environment' });
    return;
  }
  
  try {
    let targetWindow: any = window;
    
    // Check current window and parent window for wx object
    if (!targetWindow.wx && window.parent !== window) {
      targetWindow = window.parent;
    }
    
    if (!targetWindow.wx?.miniProgram) {
      options.fail?.({ message: 'WeChat Mini Program API not available' });
      return;
    }
    
    targetWindow.wx.miniProgram.navigateTo({
      url: options.url,
      extraData: options.extraData,
      envVersion: options.envVersion || 'release',
      success: options.success,
      fail: options.fail,
      complete: options.complete,
    });
  } catch (error) {
    options.fail?.(error);
  }
}

/**
 * Post message to WeChat Mini Program
 * @param data - Data to send to mini program
 * @example
 * ```typescript
 * postMessageToMiniProgram({
 *   action: 'updateUserInfo',
 *   data: { userId: 123, name: 'John' }
 * });
 * ```
 */
export function postMessageToMiniProgram(data: Record<string, any>): void {
  if (!isBrowser() || !isWechat()) {
    return;
  }
  
  try {
    let targetWindow: any = window;
    
    // Check current window and parent window for wx object
    if (!targetWindow.wx && window.parent !== window) {
      targetWindow = window.parent;
    }
    
    if (targetWindow.wx?.miniProgram) {
      targetWindow.wx.miniProgram.postMessage({ data });
    }
  } catch (error) {
    console.warn('Failed to post message to mini program:', error);
  }
}

/**
 * WeChat browser capabilities
 */
export interface WechatCapabilities {
  canUseWxPay: boolean;
  canUseJSSDK: boolean;
  canNavigateToMiniProgram: boolean;
  canPostMessage: boolean;
  version: string | null;
}

/**
 * Get WeChat browser capabilities
 * @returns Object describing available WeChat features
 */
export function getWechatCapabilities(): WechatCapabilities {
  const defaultCapabilities: WechatCapabilities = {
    canUseWxPay: false,
    canUseJSSDK: false,
    canNavigateToMiniProgram: false,
    canPostMessage: false,
    version: null,
  };
  
  if (!isBrowser() || !isWechat()) {
    return defaultCapabilities;
  }
  
  const userAgent = navigator.userAgent;
  const versionMatch = userAgent.match(/MicroMessenger\/(\d+\.\d+\.\d+)/);
  const version = versionMatch ? versionMatch[1] : null;
  
  let targetWindow: any = window;
  if (!targetWindow.wx && window.parent !== window) {
    targetWindow = window.parent;
  }
  
  return {
    canUseWxPay: typeof WeixinJSBridge !== 'undefined',
    canUseJSSDK: typeof targetWindow.wx !== 'undefined',
    canNavigateToMiniProgram: !!(targetWindow.wx?.miniProgram),
    canPostMessage: !!(targetWindow.wx?.miniProgram),
    version,
  };
}
