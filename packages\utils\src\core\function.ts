/**
 * Function utility functions
 * @module core/function
 */

/**
 * Type guard to check if a value is a function
 * @param value - The value to check
 * @returns True if the value is a function
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

/**
 * Create a debounced function that delays execution until after wait milliseconds
 * @param func - The function to debounce
 * @param wait - The number of milliseconds to delay
 * @param immediate - Whether to execute immediately on the leading edge
 * @returns The debounced function
 * @example
 * ```typescript
 * const debouncedSave = debounce(() => saveData(), 300);
 * // Will only execute saveData after 300ms of no calls
 * ```
 */
export function debounce<T extends(...args: unknown[]) => unknown>(
  func: T,
  wait: number,
  immediate = false,
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

/**
 * Create a throttled function that only executes at most once per wait milliseconds
 * @param func - The function to throttle
 * @param wait - The number of milliseconds to throttle
 * @returns The throttled function
 * @example
 * ```typescript
 * const throttledScroll = throttle(() => handleScroll(), 100);
 * // Will execute at most once every 100ms
 * ```
 */
export function throttle<T extends(...args: unknown[]) => unknown>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, wait);
    }
  };
}

/**
 * Create a memoized function that caches results based on arguments
 * @param func - The function to memoize
 * @param keyGenerator - Optional function to generate cache keys
 * @returns The memoized function
 * @example
 * ```typescript
 * const expensiveCalculation = memoize((n: number) => {
 *   // Some expensive operation
 *   return n * n;
 * });
 * 
 * expensiveCalculation(5); // Calculates and caches
 * expensiveCalculation(5); // Returns cached result
 * ```
 */
export function memoize<T extends(...args: unknown[]) => unknown>(
  func: T,
  keyGenerator?: (...args: Parameters<T>) => string,
): T & { cache: Map<string, ReturnType<T>>; clear: () => void } {
  const cache = new Map<string, ReturnType<T>>();
  
  const memoized = function (...args: Parameters<T>): ReturnType<T> {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    
    const result = func(...args) as ReturnType<T>;
    cache.set(key, result);
    return result;
  } as T & { cache: Map<string, ReturnType<T>>; clear: () => void };
  
  memoized.cache = cache;
  memoized.clear = () => cache.clear();
  
  return memoized;
}

/**
 * Create a function that can only be called once
 * @param func - The function to call once
 * @returns A function that can only be executed once
 * @example
 * ```typescript
 * const initialize = once(() => {
 *   console.log('Initialized');
 * });
 * 
 * initialize(); // Logs 'Initialized'
 * initialize(); // Does nothing
 * ```
 */
export function once<T extends(...args: unknown[]) => unknown>(
  func: T,
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  let called = false;
  let result: ReturnType<T>;
  
  return function (...args: Parameters<T>): ReturnType<T> | undefined {
    if (!called) {
      called = true;
      result = func(...args) as ReturnType<T>;
      return result;
    }
    return result;
  };
}

/**
 * Compose functions from right to left
 * @param functions - The functions to compose
 * @returns The composed function
 * @example
 * ```typescript
 * const add1 = (x: number) => x + 1;
 * const multiply2 = (x: number) => x * 2;
 * const composed = compose(add1, multiply2);
 * composed(3); // 7 (3 * 2 + 1)
 * ```
 */
export function compose<T>(...functions: Array<(arg: T) => T>): (arg: T) => T {
  return (arg: T) => functions.reduceRight((acc, fn) => fn(acc), arg);
}

/**
 * Pipe functions from left to right
 * @param functions - The functions to pipe
 * @returns The piped function
 * @example
 * ```typescript
 * const add1 = (x: number) => x + 1;
 * const multiply2 = (x: number) => x * 2;
 * const piped = pipe(add1, multiply2);
 * piped(3); // 8 ((3 + 1) * 2)
 * ```
 */
export function pipe<T>(...functions: Array<(arg: T) => T>): (arg: T) => T {
  return (arg: T) => functions.reduce((acc, fn) => fn(acc), arg);
}

/**
 * Create a curried version of a function
 * @param func - The function to curry
 * @returns The curried function
 * @example
 * ```typescript
 * const add = (a: number, b: number, c: number) => a + b + c;
 * const curriedAdd = curry(add);
 * 
 * curriedAdd(1)(2)(3); // 6
 * curriedAdd(1, 2)(3); // 6
 * curriedAdd(1)(2, 3); // 6
 * ```
 */
export function curry<T extends(...args: unknown[]) => unknown>(
  func: T,
): (...args: Partial<Parameters<T>>) => unknown {
  return function curried(...args: Partial<Parameters<T>>): unknown {
    if (args.length >= func.length) {
      return func(...(args as Parameters<T>));
    }
    
    return function (...nextArgs: Partial<Parameters<T>>) {
      return curried(...args, ...nextArgs);
    };
  };
}

/**
 * Create a function that retries execution with exponential backoff
 * @param func - The async function to retry
 * @param maxRetries - Maximum number of retries
 * @param baseDelay - Base delay in milliseconds
 * @returns A function that retries on failure
 * @example
 * ```typescript
 * const fetchWithRetry = retry(
 *   async () => fetch('/api/data'),
 *   3,
 *   1000
 * );
 * 
 * // Will retry up to 3 times with delays: 1s, 2s, 4s
 * ```
 */
export function retry<T extends(...args: unknown[]) => Promise<unknown>>(
  func: T,
  maxRetries = 3,
  baseDelay = 1000,
): (...args: Parameters<T>) => Promise<Awaited<ReturnType<T>>> {
  return async function (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await func(...args) as Awaited<ReturnType<T>>;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  };
}

/**
 * Create a function with timeout
 * @param func - The async function to add timeout to
 * @param timeoutMs - Timeout in milliseconds
 * @returns A function that times out after specified duration
 * @example
 * ```typescript
 * const fetchWithTimeout = timeout(
 *   async () => fetch('/api/slow'),
 *   5000
 * );
 * 
 * // Will timeout after 5 seconds
 * ```
 */
export function timeout<T extends(...args: unknown[]) => Promise<unknown>>(
  func: T,
  timeoutMs: number,
): (...args: Parameters<T>) => Promise<Awaited<ReturnType<T>>> {
  return async function (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> {
    return Promise.race([
      func(...args) as Promise<Awaited<ReturnType<T>>>,
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`Function timed out after ${timeoutMs}ms`)), timeoutMs),
      ),
    ]);
  };
}
