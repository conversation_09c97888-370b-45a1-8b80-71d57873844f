/**
 * Performance optimization utilities for deferred rendering
 * @module performance/defer
 */

/**
 * useDefer return value interface
 */
export interface UseDeferReturn {
  /**
   * Check if element at index should be rendered
   * @param n - Element index (0-based)
   * @returns True if element should be rendered
   */
  defer: (n: number) => boolean;
  
  /**
   * Get current frame count (for debugging)
   * @returns Current frame number
   */
  getCurrentFrame: () => number;
  
  /**
   * Reset frame counter (for testing)
   * @param value - Reset value (default: 1)
   */
  resetFrameCount: (value?: number) => void;
  
  /**
   * Pause the frame counter
   */
  pause: () => void;
  
  /**
   * Resume the frame counter
   */
  resume: () => void;
  
  /**
   * Cleanup function to stop the counter and cancel animation frames
   */
  cleanup: () => void;
}

/**
 * Deferred rendering hook for performance optimization
 * 
 * Implements frame-based rendering to prevent UI blocking when rendering
 * large amounts of content. Each frame renders one additional item.
 * 
 * @returns Object with defer function and control methods
 * 
 * @example
 * ```typescript
 * // Basic usage
 * const { defer, cleanup } = useDefer();
 * 
 * // In render loop
 * items.forEach((item, index) => {
 *   if (defer(index)) {
 *     renderItem(item);
 *   }
 * });
 * 
 * // Cleanup when done
 * cleanup();
 * ```
 * 
 * @example
 * ```vue
 * <!-- Vue 3 Composition API -->
 * <template>
 *   <div class="list">
 *     <div v-for="(item, index) in items" :key="item.id">
 *       <ItemComponent v-if="defer(index)" :item="item" />
 *     </div>
 *   </div>
 * </template>
 * 
 * <script setup>
 * import { onUnmounted } from 'vue';
 * import { useDefer } from '@nuas/utils';
 * 
 * const { defer, cleanup } = useDefer();
 * 
 * onUnmounted(() => {
 *   cleanup();
 * });
 * </script>
 * ```
 * 
 * @example
 * ```tsx
 * // React usage
 * import { useEffect } from 'react';
 * import { useDefer } from '@nuas/utils';
 * 
 * function LargeList({ items }) {
 *   const { defer, cleanup } = useDefer();
 *   
 *   useEffect(() => {
 *     return cleanup; // Cleanup on unmount
 *   }, [cleanup]);
 *   
 *   return (
 *     <div>
 *       {items.map((item, index) => (
 *         defer(index) && <Item key={item.id} data={item} />
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
export function useDefer(): UseDeferReturn {
  let count = 1;
  let isRunning = true;
  let rafId: number | null = null;
  
  /**
   * Update frame counter using requestAnimationFrame
   */
  function update(): void {
    count++;
    if (isRunning) {
      rafId = requestAnimationFrame(update);
    }
  }
  
  // Start the counter
  rafId = requestAnimationFrame(update);
  
  /**
   * Check if element should be rendered based on current frame
   */
  function defer(n: number): boolean {
    // Input validation
    if (typeof n !== 'number' || n < 0) {
      if (process.env.NODE_ENV !== 'production') {
        console.warn('useDefer: Parameter n must be a non-negative number');
      }
      return false;
    }
    
    return count >= n;
  }
  
  /**
   * Get current frame count
   */
  function getCurrentFrame(): number {
    return count;
  }
  
  /**
   * Reset frame counter
   */
  function resetFrameCount(value = 1): void {
    count = Math.max(1, value);
  }
  
  /**
   * Pause frame counting
   */
  function pause(): void {
    isRunning = false;
  }
  
  /**
   * Resume frame counting
   */
  function resume(): void {
    if (!isRunning) {
      isRunning = true;
      rafId = requestAnimationFrame(update);
    }
  }
  
  /**
   * Cleanup resources
   */
  function cleanup(): void {
    isRunning = false;
    
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  }
  
  return {
    defer,
    getCurrentFrame,
    resetFrameCount,
    pause,
    resume,
    cleanup,
  };
}

/**
 * Configuration options for batch processing
 */
export interface BatchProcessOptions {
  batchSize?: number;
  delay?: number;
  onProgress?: (processed: number, total: number) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Process items in batches to prevent UI blocking
 * 
 * @param items - Array of items to process
 * @param processor - Function to process each item
 * @param options - Processing options
 * @returns Promise that resolves when all items are processed
 * 
 * @example
 * ```typescript
 * const items = Array.from({ length: 10000 }, (_, i) => i);
 * 
 * await batchProcess(
 *   items,
 *   (item) => {
 *     // Process each item
 *     console.log(`Processing item ${item}`);
 *   },
 *   {
 *     batchSize: 100,
 *     delay: 10,
 *     onProgress: (processed, total) => {
 *       console.log(`Progress: ${processed}/${total}`);
 *     }
 *   }
 * );
 * ```
 */
export async function batchProcess<T>(
  items: T[],
  processor: (item: T, index: number) => void | Promise<void>,
  options: BatchProcessOptions = {},
): Promise<void> {
  const {
    batchSize = 100,
    delay = 10,
    onProgress,
    onComplete,
    onError,
  } = options;
  
  const total = items.length;
  let processed = 0;
  
  try {
    for (let i = 0; i < total; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      // Process current batch
      await Promise.all(
        batch.map((item, batchIndex) => 
          processor(item, i + batchIndex),
        ),
      );
      
      processed += batch.length;
      onProgress?.(processed, total);
      
      // Yield control to browser if there are more items
      if (i + batchSize < total) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    onComplete?.();
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    onError?.(err);
    throw err;
  }
}

/**
 * Create a throttled version of requestAnimationFrame
 * 
 * @param callback - Function to call on each frame
 * @param fps - Target frames per second (default: 60)
 * @returns Cleanup function
 * 
 * @example
 * ```typescript
 * const cleanup = throttledRAF(() => {
 *   // Animation logic here
 *   updateAnimation();
 * }, 30); // 30 FPS
 * 
 * // Later, stop the animation
 * cleanup();
 * ```
 */
export function throttledRAF(
  callback: () => void,
  fps = 60,
): () => void {
  const interval = 1000 / fps;
  let lastTime = 0;
  let rafId: number | null = null;
  let isRunning = true;
  
  function frame(currentTime: number): void {
    if (!isRunning) return;
    
    if (currentTime - lastTime >= interval) {
      callback();
      lastTime = currentTime;
    }
    
    rafId = requestAnimationFrame(frame);
  }
  
  rafId = requestAnimationFrame(frame);
  
  return () => {
    isRunning = false;
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  };
}

/**
 * Debounced version of requestAnimationFrame
 * 
 * @param callback - Function to call
 * @returns Function to trigger the callback
 * 
 * @example
 * ```typescript
 * const debouncedUpdate = debouncedRAF(() => {
 *   // Expensive DOM updates
 *   updateLayout();
 * });
 * 
 * // Multiple calls will be batched into single frame
 * debouncedUpdate();
 * debouncedUpdate();
 * debouncedUpdate(); // Only one update will happen
 * ```
 */
export function debouncedRAF(callback: () => void): () => void {
  let rafId: number | null = null;
  
  return () => {
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
    }
    
    rafId = requestAnimationFrame(() => {
      rafId = null;
      callback();
    });
  };
}
