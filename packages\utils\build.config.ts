import { defineBuildConfig } from 'unbuild';

export default defineBuildConfig({
  entries: [
    // Main entry point
    {
      input: 'src/index',
      name: 'index',
    },
    // Browser-only entry point
    {
      input: 'src/browser/index',
      name: 'browser/index',
    },
    // Core utilities (universal)
    {
      input: 'src/core/index',
      name: 'core/index',
    },
    // Performance utilities
    {
      input: 'src/performance/index',
      name: 'performance/index',
    },
  ],
  clean: true,
  declaration: true,
  rollup: {
    emitCJS: true,
    esbuild: {
      target: 'es2020',
      minify: true,
    },
    dts: {
      respectExternal: true,
    },
  },
  outDir: 'dist',
  externals: [],
  failOnWarn: false,
});
