import * as fs from 'node:fs';
import * as path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const srcDir = path.join(__dirname, '../src');
const indexPath = path.join(srcDir, 'index.ts');

// 获取所有 .ts 文件（排除 index.ts 和类型定义文件）
const getTsFiles = (dir: string): string[] => {
  const files = fs.readdirSync(dir);
  return files
    .filter(file => file.endsWith('.ts') && !file.endsWith('.d.ts') && file !== 'index.ts')
    .map(file => path.join(dir, file));
};

// 生成导出语句
const generateExports = (): string => {
  const files = getTsFiles(srcDir);
  return files
    .map(file => {
      const relativePath = path.relative(srcDir, file);
      const moduleName = path.basename(file, '.ts');
      return `export * from './${moduleName}';`;
    })
    .join('\n');
};

// 写入 index.ts
const content = generateExports();
fs.writeFileSync(indexPath, content);

console.log('✅ 导出文件已更新'); 