module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: './tsconfig.json',
  },
  plugins: ['@typescript-eslint'],
  rules: {
    // TypeScript 规则 - 工具库标准（平衡严格性和实用性）
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': ['warn', {
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/explicit-module-boundary-types': 'warn',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    '@typescript-eslint/prefer-nullish-coalescing': 'off',
    '@typescript-eslint/prefer-optional-chain': 'warn',
    '@typescript-eslint/no-floating-promises': 'off',
    '@typescript-eslint/await-thenable': 'off',
    '@typescript-eslint/no-misused-promises': 'off',

    // 代码质量规则 - 工具库标准
    'no-console': 'warn', // 工具库中允许少量 console，但要警告
    'no-debugger': 'error',
    'no-alert': 'error',
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',

    // 代码风格 - 严格统一
    'indent': ['error', 2, { SwitchCase: 1 }],
    'quotes': ['error', 'single', { avoidEscape: true }],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'always-multiline'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    'space-before-function-paren': ['error', {
      anonymous: 'always',
      named: 'never',
      asyncArrow: 'always',
    }],

    // 最佳实践 - 工具库专用
    'eqeqeq': ['error', 'always'],
    'no-var': 'error',
    'prefer-const': 'error',
    'prefer-arrow-callback': 'error',
    'prefer-template': 'error',
    'prefer-spread': 'error',
    'prefer-rest-params': 'error',
    'no-param-reassign': 'error',
    'no-return-assign': 'error',
    'no-throw-literal': 'error',

    // 文档要求 - 工具库建议有文档（不强制）
    'valid-jsdoc': 'off', // 使用 TypeScript 类型即可
    'require-jsdoc': 'off', // 不强制 JSDoc，TypeScript 类型更重要

    // 性能相关
    'no-loop-func': 'error',
    'no-new-object': 'error',
    'no-array-constructor': 'error',
    'no-new-wrappers': 'error',
  },
  overrides: [
    // 测试文件特殊配置
    {
      files: ['**/*.test.ts', '**/*.spec.ts', '**/test/**/*.ts'],
      env: {
        jest: true,
        mocha: true,
      },
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off',
        'require-jsdoc': 'off',
        'valid-jsdoc': 'off',
      },
    },
    // 示例文件特殊配置
    {
      files: ['examples/**/*.ts', 'examples/**/*.js'],
      rules: {
        'no-console': 'off',
        '@typescript-eslint/no-unused-vars': 'off',
        'require-jsdoc': 'off',
        'valid-jsdoc': 'off',
        '@typescript-eslint/explicit-function-return-type': 'off',
      },
    },
    // 构建配置文件
    {
      files: ['build.config.ts', 'unbuild.config.ts'],
      rules: {
        'require-jsdoc': 'off',
        'valid-jsdoc': 'off',
        '@typescript-eslint/explicit-function-return-type': 'off',
      },
    },
  ],
  ignorePatterns: [
    'dist/',
    'node_modules/',
    'coverage/',
    '*.min.js',
    '*.d.ts',
  ],
};
