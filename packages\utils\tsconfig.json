{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "allowJs": false,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": ".",
    // 严格的类型检查 - 适合工具库
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedIndexedAccess": true,
    // 工具库特定配置
    "removeComments": false,
    "preserveConstEnums": true,
    "isolatedModules": true,
    "verbatimModuleSyntax": false,
    // 路径映射
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@/types/*": [
        "./src/types/*"
      ]
    }
  },
  "include": [
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "build.config.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts",
    "examples"
  ],
  "ts-node": {
    "esm": true
  }
}
