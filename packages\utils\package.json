{"name": "@nuas/utils", "version": "1.0.0", "description": "通用工具函数库", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "type": "module", "files": ["dist"], "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./browser": {"import": "./dist/browser/index.mjs", "require": "./dist/browser/index.cjs", "types": "./dist/browser/index.d.ts"}, "./core": {"import": "./dist/core/index.mjs", "require": "./dist/core/index.cjs", "types": "./dist/core/index.d.ts"}, "./performance": {"import": "./dist/performance/index.mjs", "require": "./dist/performance/index.cjs", "types": "./dist/performance/index.d.ts"}, "./package.json": "./package.json"}, "scripts": {"build": "unbuild", "build:watch": "unbuild --watch", "dev": "unbuild --stub", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "lint:check": "eslint src --ext .ts --max-warnings 0", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "size": "size-limit", "analyze": "size-limit --why", "docs": "typedoc", "docs:serve": "typedoc --watch", "release": "npm run lint:check && npm run type-check && npm run test:run && npm run build", "prepublishOnly": "npm run release"}, "keywords": ["utils", "utilities", "typescript", "browser", "wechat", "miniprogram", "performance", "tree-shaking", "esm", "cjs"], "author": "NUAS Team", "license": "MIT", "homepage": "https://github.com/nuas/utils#readme", "repository": {"type": "git", "url": "https://github.com/nuas/utils.git"}, "bugs": {"url": "https://github.com/nuas/utils/issues"}, "engines": {"node": ">=16.0.0"}, "sideEffects": false, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "rimraf": "^5.0.0", "typescript": "^5.0.0", "unbuild": "^3.5.0"}}